{"dashboard": {"id": null, "title": "CVLeap AI Agents Dashboard", "tags": ["cvleap", "agents", "ai"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Agent System Overview", "type": "stat", "targets": [{"expr": "cvleap_agents_total", "legendFormat": "Total Agents"}, {"expr": "cvleap_agents_active", "legendFormat": "Active Agents"}, {"expr": "cvleap_pipelines_active", "legendFormat": "Active Pipelines"}, {"expr": "cvleap_tasks_queued", "legendFormat": "Queued Tasks"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 2, "title": "Task Completion Rate", "type": "timeseries", "targets": [{"expr": "rate(cvleap_tasks_completed_total[5m])", "legendFormat": "Tasks Completed/sec"}, {"expr": "rate(cvleap_tasks_failed_total[5m])", "legendFormat": "Tasks Failed/sec"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}}}, {"id": 3, "title": "Agent Resource Usage", "type": "timeseries", "targets": [{"expr": "cvleap_agent_memory_usage_bytes / 1024 / 1024", "legendFormat": "Memory Usage (MB)"}, {"expr": "cvleap_agent_cpu_usage_percent", "legendFormat": "CPU Usage (%)"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}}, {"id": 4, "title": "Job Application Success Metrics", "type": "stat", "targets": [{"expr": "cvleap_applications_submitted_total", "legendFormat": "Total Applications"}, {"expr": "cvleap_applications_response_rate", "legendFormat": "Response Rate (%)"}, {"expr": "cvleap_applications_interview_rate", "legendFormat": "Interview Rate (%)"}, {"expr": "cvleap_applications_offer_rate", "legendFormat": "Offer Rate (%)"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 20}]}, "unit": "percent"}}}, {"id": 5, "title": "Agent Task Distribution", "type": "piechart", "targets": [{"expr": "sum by (agent_type) (cvleap_agent_tasks_total)", "legendFormat": "{{agent_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}}}, {"id": 6, "title": "Queue Depth by Priority", "type": "bargauge", "targets": [{"expr": "cvleap_queue_depth{priority=\"urgent\"}", "legendFormat": "<PERSON><PERSON>"}, {"expr": "cvleap_queue_depth{priority=\"high\"}", "legendFormat": "High"}, {"expr": "cvleap_queue_depth{priority=\"medium\"}", "legendFormat": "Medium"}, {"expr": "cvleap_queue_depth{priority=\"low\"}", "legendFormat": "Low"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}, {"id": 7, "title": "Browser Session Metrics", "type": "timeseries", "targets": [{"expr": "cvleap_browser_sessions_active", "legendFormat": "Active Sessions"}, {"expr": "cvleap_browser_sessions_total", "legendFormat": "Total Sessions"}, {"expr": "rate(cvleap_browser_sessions_failed_total[5m])", "legendFormat": "Failed Sessions/sec"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 8, "title": "Portal Adapter Performance", "type": "table", "targets": [{"expr": "sum by (portal_type) (cvleap_portal_applications_total)", "legendFormat": "{{portal_type}}"}, {"expr": "sum by (portal_type) (cvleap_portal_applications_success_total) / sum by (portal_type) (cvleap_portal_applications_total) * 100", "legendFormat": "{{portal_type}} Success Rate"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 9, "title": "Error Rate by Agent Type", "type": "timeseries", "targets": [{"expr": "rate(cvleap_agent_errors_total{agent_type=\"planning\"}[5m])", "legendFormat": "Planning Agent"}, {"expr": "rate(cvleap_agent_errors_total{agent_type=\"research\"}[5m])", "legendFormat": "Research Agent"}, {"expr": "rate(cvleap_agent_errors_total{agent_type=\"execution\"}[5m])", "legendFormat": "Execution Agent"}, {"expr": "rate(cvleap_agent_errors_total{agent_type=\"resume_optimization\"}[5m])", "legendFormat": "Resume Optimization Agent"}, {"expr": "rate(cvleap_agent_errors_total{agent_type=\"monitoring\"}[5m])", "legendFormat": "Monitoring Agent"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}}}], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(cvleap_agent_tasks_total, agent_type)", "hide": 0, "includeAll": true, "label": "Agent Type", "multi": true, "name": "agent_type", "options": [], "query": {"query": "label_values(cvleap_agent_tasks_total, agent_type)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(cvleap_portal_applications_total, portal_type)", "hide": 0, "includeAll": true, "label": "Portal Type", "multi": true, "name": "portal_type", "options": [], "query": {"query": "label_values(cvleap_portal_applications_total, portal_type)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "liveNow": false, "schemaVersion": 27, "version": 1, "weekStart": ""}}