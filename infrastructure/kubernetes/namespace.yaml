apiVersion: v1
kind: Namespace
metadata:
  name: cvleap-agents
  labels:
    name: cvleap-agents
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: cvleap-quota
  namespace: cvleap-agents
spec:
  hard:
    requests.cpu: "100"
    requests.memory: 200Gi
    limits.cpu: "200"
    limits.memory: 400Gi
    pods: "1000"
    persistentvolumeclaims: "100"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: cvleap-limits
  namespace: cvleap-agents
spec:
  limits:
  - default:
      cpu: "2"
      memory: "4Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
