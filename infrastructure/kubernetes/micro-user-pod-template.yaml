# CVLeap Micro User POD Template - Cost-Optimized for Centralized Autonomous Agents
# Reduced resource footprint with server-side agent communication

apiVersion: v1
kind: ConfigMap
metadata:
  name: micro-user-pod-config
  namespace: cvleap-agents
data:
  browser-config.json: |
    {
      "headless": true,
      "args": [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--single-process",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--memory-pressure-off",
        "--max_old_space_size=512"
      ],
      "defaultViewport": {
        "width": 1366,
        "height": 768
      }
    }
  
  agent-endpoints.json: |
    {
      "autonomous_agents_server": "http://autonomous-agents-server:8080",
      "grpc_endpoint": "autonomous-agents-server:9000",
      "metrics_endpoint": "http://autonomous-agents-server:9090/metrics",
      "session_timeout": 3600,
      "max_retries": 3,
      "connection_pool_size": 10
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: micro-user-pod-template
  namespace: cvleap-agents
  labels:
    app: micro-user-pod
    component: user-session
    tier: execution
spec:
  replicas: 0  # Scaled dynamically based on active users
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      app: micro-user-pod
      component: user-session
  template:
    metadata:
      labels:
        app: micro-user-pod
        component: user-session
        tier: execution
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: cvleap-agent-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: user-session-handler
        image: cvleap/micro-user-pod:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        - name: chrome-debug
          containerPort: 9222
          protocol: TCP
        resources:
          requests:
            cpu: "250m"      # Reduced from 1 CPU (75% reduction)
            memory: "512Mi"  # Reduced from 2Gi (75% reduction)
          limits:
            cpu: "500m"      # Reduced from 4 CPU (87.5% reduction)
            memory: "1Gi"    # Reduced from 8Gi (87.5% reduction)
        env:
        - name: USER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['user-id']
        - name: SESSION_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['session-id']
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: AUTONOMOUS_AGENTS_ENDPOINT
          value: "http://autonomous-agents-server:8080"
        - name: AUTONOMOUS_AGENTS_GRPC
          value: "autonomous-agents-server:9000"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: SESSION_TIMEOUT
          value: "3600"
        - name: AGENT_MODE
          value: "client"  # Client mode - connects to centralized agents
        - name: RESOURCE_OPTIMIZATION
          value: "true"
        - name: BROWSER_MEMORY_LIMIT
          value: "256"  # MB
        - name: MAX_CONCURRENT_TABS
          value: "3"
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - name: tmp-volume
          mountPath: /tmp
          subPath: tmp
        - name: browser-data
          mountPath: /home/<USER>/.config
          subPath: browser-config
        - name: screenshots
          mountPath: /app/screenshots
          subPath: screenshots
        livenessProbe:
          httpGet:
            path: /health/live
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 2
        startupProbe:
          httpGet:
            path: /health/startup
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 6
      volumes:
      - name: config-volume
        configMap:
          name: micro-user-pod-config
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi  # Reduced from 5Gi
      - name: browser-data
        emptyDir:
          sizeLimit: 512Mi  # Reduced from 2Gi
      - name: screenshots
        emptyDir:
          sizeLimit: 256Mi  # Reduced from 1Gi
      # Optimize pod placement for cost efficiency
      nodeSelector:
        node-type: standard  # Use standard nodes, not GPU nodes
      tolerations:
      - key: "cost-optimized"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        # Prefer to schedule multiple micro-PODs on same node
        podAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 80
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - micro-user-pod
              topologyKey: kubernetes.io/hostname
        # Avoid scheduling on nodes with autonomous agents
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - autonomous-agents
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: micro-user-pod-service
  namespace: cvleap-agents
  labels:
    app: micro-user-pod
    component: user-session
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  - name: chrome-debug
    port: 9222
    targetPort: 9222
    protocol: TCP
  selector:
    app: micro-user-pod
    component: user-session

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: micro-user-pod-hpa
  namespace: cvleap-agents
  labels:
    app: micro-user-pod
    component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: micro-user-pod-template
  minReplicas: 0
  maxReplicas: 1000
  metrics:
  - type: External
    external:
      metric:
        name: active_user_sessions
      target:
        type: AverageValue
        averageValue: "1"  # 1 POD per active user session
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 180  # 3 minutes
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 30
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 10
        periodSeconds: 15

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: micro-user-pod-network-policy
  namespace: cvleap-agents
spec:
  podSelector:
    matchLabels:
      app: micro-user-pod
      component: user-session
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from load balancer/ingress
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  # Allow monitoring traffic
  - from:
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 3000
  egress:
  # Allow communication with autonomous agents server
  - to:
    - podSelector:
        matchLabels:
          app: autonomous-agents
          component: server
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9000
  # Allow external web traffic for job applications
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  # Allow database connections
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  # Allow Redis connections
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53

---
# Pod Disruption Budget for high availability
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: micro-user-pod-pdb
  namespace: cvleap-agents
spec:
  minAvailable: 80%
  selector:
    matchLabels:
      app: micro-user-pod
      component: user-session

---
# Resource optimization policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: resource-optimization-policy
  namespace: cvleap-agents
data:
  optimization.yaml: |
    # Pod consolidation settings
    max_pods_per_node: 12  # 12 micro-PODs per standard node
    preferred_node_utilization: 0.8
    
    # Resource sharing settings
    enable_resource_sharing: true
    cpu_overcommit_ratio: 1.5
    memory_overcommit_ratio: 1.2
    
    # Cost optimization
    prefer_spot_instances: true
    enable_vertical_pod_autoscaling: true
    resource_request_optimization: true
