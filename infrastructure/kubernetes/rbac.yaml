apiVersion: v1
kind: ServiceAccount
metadata:
  name: cvleap-agent-service-account
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: service-account
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cvleap-agents
  name: cvleap-agent-role
  labels:
    app: cvleap-agents
    component: rbac
rules:
# Pod management permissions
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods/status"]
  verbs: ["get", "update", "patch"]

# ConfigMap and Secret access
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]

# Service access
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]

# Event creation for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

# Deployment management for scaling
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "update", "patch"]
- apiGroups: ["apps"]
  resources: ["deployments/scale"]
  verbs: ["get", "update", "patch"]

# ReplicaSet access
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["get", "list", "watch"]

# HorizontalPodAutoscaler access
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]

# Metrics access for monitoring
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cvleap-agent-role-binding
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: rbac
subjects:
- kind: ServiceAccount
  name: cvleap-agent-service-account
  namespace: cvleap-agents
roleRef:
  kind: Role
  name: cvleap-agent-role
  apiGroup: rbac.authorization.k8s.io
---
# Cluster-level permissions for node metrics and cluster info
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cvleap-agent-cluster-role
  labels:
    app: cvleap-agents
    component: rbac
rules:
# Node metrics access
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes/metrics"]
  verbs: ["get", "list"]

# Cluster metrics access
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes"]
  verbs: ["get", "list"]

# Custom metrics access
- apiGroups: ["custom.metrics.k8s.io"]
  resources: ["*"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cvleap-agent-cluster-role-binding
  labels:
    app: cvleap-agents
    component: rbac
subjects:
- kind: ServiceAccount
  name: cvleap-agent-service-account
  namespace: cvleap-agents
roleRef:
  kind: ClusterRole
  name: cvleap-agent-cluster-role
  apiGroup: rbac.authorization.k8s.io
---
# Service account for monitoring components
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cvleap-monitoring-service-account
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cvleap-monitoring-cluster-role
  labels:
    app: cvleap-agents
    component: monitoring
rules:
# Prometheus monitoring permissions
- apiGroups: [""]
  resources: ["nodes", "nodes/proxy", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes/metrics"]
  verbs: ["get"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cvleap-monitoring-cluster-role-binding
  labels:
    app: cvleap-agents
    component: monitoring
subjects:
- kind: ServiceAccount
  name: cvleap-monitoring-service-account
  namespace: cvleap-agents
roleRef:
  kind: ClusterRole
  name: cvleap-monitoring-cluster-role
  apiGroup: rbac.authorization.k8s.io
---
# Service account for user pods
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cvleap-user-pod-service-account
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: user-pod
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cvleap-agents
  name: cvleap-user-pod-role
  labels:
    app: cvleap-agents
    component: user-pod
rules:
# Limited permissions for user pods
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
  resourceNames: [] # Restrict to own pod if needed

# ConfigMap access for configuration
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["browser-config", "agent-config"]

# Secret access for credentials
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["ai-credentials", "database-credentials", "redis-credentials"]

# Event creation for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cvleap-user-pod-role-binding
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: user-pod
subjects:
- kind: ServiceAccount
  name: cvleap-user-pod-service-account
  namespace: cvleap-agents
roleRef:
  kind: Role
  name: cvleap-user-pod-role
  apiGroup: rbac.authorization.k8s.io
---
# Network policies for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cvleap-agents-network-policy
  namespace: cvleap-agents
  labels:
    app: cvleap-agents
    component: security
spec:
  podSelector:
    matchLabels:
      app: cvleap-agents
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from within the namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: cvleap-agents
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9090
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow egress to DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # Allow egress to external services (job portals, APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  # Allow egress to database and Redis
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
  # Allow egress within namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: cvleap-agents
---
# Pod Security Policy (if PSP is enabled)
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: cvleap-agents-psp
  labels:
    app: cvleap-agents
    component: security
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
---
# ClusterRole for PSP
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cvleap-agents-psp-role
  labels:
    app: cvleap-agents
    component: security
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - cvleap-agents-psp
---
# ClusterRoleBinding for PSP
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cvleap-agents-psp-binding
  labels:
    app: cvleap-agents
    component: security
roleRef:
  kind: ClusterRole
  name: cvleap-agents-psp-role
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: cvleap-agent-service-account
  namespace: cvleap-agents
- kind: ServiceAccount
  name: cvleap-user-pod-service-account
  namespace: cvleap-agents
- kind: ServiceAccount
  name: cvleap-monitoring-service-account
  namespace: cvleap-agents
