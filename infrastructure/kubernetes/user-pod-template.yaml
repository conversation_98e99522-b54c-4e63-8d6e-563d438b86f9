apiVersion: v1
kind: ConfigMap
metadata:
  name: user-pod-config
  namespace: cvleap-agents
data:
  browser-config.json: |
    {
      "headless": true,
      "args": [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--single-process",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor"
      ],
      "defaultViewport": {
        "width": 1920,
        "height": 1080
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-pod-template
  namespace: cvleap-agents
spec:
  replicas: 0  # Scaled dynamically
  selector:
    matchLabels:
      app: user-pod
  template:
    metadata:
      labels:
        app: user-pod
        tier: execution
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: job-agent-executor
        image: cvleap/job-agent:latest
        imagePullPolicy: Always
        resources:
          requests:
            cpu: "1"
            memory: "2Gi"
          limits:
            cpu: "4"
            memory: "8Gi"
        env:
        - name: USER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['user-id']
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-credentials
              key: openai-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-credentials
              key: anthropic-key
        volumeMounts:
        - name: browser-config
          mountPath: /app/config
        - name: user-data
          mountPath: /app/data
        - name: temp-storage
          mountPath: /tmp
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 9222
          name: chrome-debug
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: browser-config
        configMap:
          name: user-pod-config
      - name: user-data
        persistentVolumeClaim:
          claimName: user-data-pvc
      - name: temp-storage
        emptyDir:
          sizeLimit: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: user-pod-service
  namespace: cvleap-agents
spec:
  selector:
    app: user-pod
  ports:
  - name: http
    port: 80
    targetPort: 3000
  - name: chrome-debug
    port: 9222
    targetPort: 9222
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-pod-hpa
  namespace: cvleap-agents
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-pod-template
  minReplicas: 0
  maxReplicas: 100
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
