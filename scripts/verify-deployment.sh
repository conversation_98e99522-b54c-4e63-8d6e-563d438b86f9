#!/bin/bash

# CVLeap Agent System Deployment Verification Script
# Verifies POD systems, human handoff visibility, and automation capabilities

set -e

# Make script executable
chmod +x "$0"

echo "🚀 CVLeap Agent System Deployment Verification"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE=${NAMESPACE:-"cvleap-agents"}
TIMEOUT=${TIMEOUT:-"300"}
DASHBOARD_URL=${DASHBOARD_URL:-"http://localhost:3000"}

# Function to print status
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GRE<PERSON>}✅ $message${NC}"
            ;;
        "FAILURE")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to check if command exists
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_status "FAILURE" "$1 command not found. Please install $1."
        exit 1
    fi
}

# Function to wait for pod to be ready
wait_for_pod() {
    local pod_name=$1
    local namespace=$2
    local timeout=$3
    
    print_status "INFO" "Waiting for pod $pod_name to be ready..."
    
    if kubectl wait --for=condition=ready pod -l app=$pod_name -n $namespace --timeout=${timeout}s; then
        print_status "SUCCESS" "Pod $pod_name is ready"
        return 0
    else
        print_status "FAILURE" "Pod $pod_name failed to become ready within ${timeout}s"
        return 1
    fi
}

# Function to test API endpoint
test_api_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    print_status "INFO" "Testing $description: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" $url || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        print_status "SUCCESS" "$description is responding correctly (HTTP $response)"
        return 0
    else
        print_status "FAILURE" "$description failed (HTTP $response, expected $expected_status)"
        return 1
    fi
}

# Function to test human handoff visibility
test_handoff_visibility() {
    print_status "INFO" "Testing human handoff visibility..."
    
    # Create a test handoff request
    local handoff_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "agentId": "test-agent-1",
            "agentType": "execution",
            "userId": "test-user-1",
            "context": {
                "situation": "Test handoff for deployment verification",
                "availableActions": ["approve", "reject"],
                "constraints": {}
            },
            "decision": {
                "action": "approve",
                "confidence": 0.4,
                "reasoning": "Low confidence test decision",
                "alternatives": [],
                "requiresHumanApproval": true,
                "metadata": {}
            },
            "reason": "Deployment verification test"
        }' \
        "${DASHBOARD_URL}/api/handoffs" || echo '{"error": "failed"}')
    
    if echo "$handoff_response" | grep -q '"id"'; then
        local handoff_id=$(echo "$handoff_response" | jq -r '.id' 2>/dev/null || echo "unknown")
        print_status "SUCCESS" "Human handoff request created successfully (ID: $handoff_id)"
        
        # Test handoff visibility
        local handoff_list=$(curl -s "${DASHBOARD_URL}/api/handoffs" || echo '{"error": "failed"}')
        if echo "$handoff_list" | grep -q "$handoff_id"; then
            print_status "SUCCESS" "Human handoff is visible in dashboard"
        else
            print_status "WARNING" "Human handoff not immediately visible (may be eventual consistency)"
        fi
        
        return 0
    else
        print_status "FAILURE" "Failed to create human handoff request"
        return 1
    fi
}

# Function to test agent autonomy
test_agent_autonomy() {
    print_status "INFO" "Testing agent autonomous decision making..."
    
    # Test AI decision engine
    local decision_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "situation": "Test autonomous decision making",
            "availableActions": ["action1", "action2", "action3"],
            "constraints": {"testMode": true},
            "urgency": "medium"
        }' \
        "${DASHBOARD_URL}/api/agents/decision" || echo '{"error": "failed"}')
    
    if echo "$decision_response" | grep -q '"action"'; then
        local action=$(echo "$decision_response" | jq -r '.action' 2>/dev/null || echo "unknown")
        local confidence=$(echo "$decision_response" | jq -r '.confidence' 2>/dev/null || echo "0")
        print_status "SUCCESS" "Agent made autonomous decision: $action (confidence: $confidence)"
        return 0
    else
        print_status "FAILURE" "Agent failed to make autonomous decision"
        return 1
    fi
}

# Function to test POD isolation
test_pod_isolation() {
    print_status "INFO" "Testing POD isolation and resource allocation..."
    
    # Check if pods are running in isolated namespaces
    local pod_count=$(kubectl get pods -n $NAMESPACE --no-headers 2>/dev/null | wc -l || echo "0")
    
    if [ "$pod_count" -gt "0" ]; then
        print_status "SUCCESS" "Found $pod_count pods running in namespace $NAMESPACE"
        
        # Check resource limits
        local pods_with_limits=$(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[*].resources.limits}' 2>/dev/null | grep -c "cpu\|memory" || echo "0")
        
        if [ "$pods_with_limits" -gt "0" ]; then
            print_status "SUCCESS" "Pods have resource limits configured"
        else
            print_status "WARNING" "Pods may not have resource limits configured"
        fi
        
        return 0
    else
        print_status "FAILURE" "No pods found in namespace $NAMESPACE"
        return 1
    fi
}

# Function to test monitoring and metrics
test_monitoring() {
    print_status "INFO" "Testing monitoring and metrics collection..."
    
    # Test Prometheus metrics endpoint
    if test_api_endpoint "${DASHBOARD_URL}/metrics" "200" "Prometheus metrics"; then
        # Test Grafana dashboard
        if test_api_endpoint "${DASHBOARD_URL}/grafana" "200" "Grafana dashboard"; then
            print_status "SUCCESS" "Monitoring stack is operational"
            return 0
        else
            print_status "WARNING" "Grafana dashboard not accessible"
            return 1
        fi
    else
        print_status "FAILURE" "Prometheus metrics not available"
        return 1
    fi
}

# Function to test queue system
test_queue_system() {
    print_status "INFO" "Testing Redis queue system..."
    
    # Test queue health
    local queue_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "type": "task",
            "agentType": "planning",
            "taskType": "test_task",
            "data": {"test": true},
            "priority": "medium"
        }' \
        "${DASHBOARD_URL}/api/agents" || echo '{"error": "failed"}')
    
    if echo "$queue_response" | grep -q '"taskId"'; then
        local task_id=$(echo "$queue_response" | jq -r '.taskId' 2>/dev/null || echo "unknown")
        print_status "SUCCESS" "Task queued successfully (ID: $task_id)"
        
        # Check task status
        sleep 2
        local task_status=$(curl -s "${DASHBOARD_URL}/api/agents/planning/tasks/$task_id" || echo '{"error": "failed"}')
        if echo "$task_status" | grep -q '"status"'; then
            print_status "SUCCESS" "Task status tracking is working"
        else
            print_status "WARNING" "Task status tracking may have issues"
        fi
        
        return 0
    else
        print_status "FAILURE" "Failed to queue task"
        return 1
    fi
}

# Main verification process
main() {
    print_status "INFO" "Starting CVLeap Agent System verification..."
    
    # Check prerequisites
    print_status "INFO" "Checking prerequisites..."
    check_command "kubectl"
    check_command "curl"
    check_command "jq"
    
    # Initialize counters
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # Test 1: POD System
    print_status "INFO" "Test 1: POD System and Isolation"
    total_tests=$((total_tests + 1))
    if test_pod_isolation; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 2: Agent Services
    print_status "INFO" "Test 2: Agent Services Health"
    total_tests=$((total_tests + 1))
    if test_api_endpoint "${DASHBOARD_URL}/api/agents" "200" "Agent API"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 3: Human Handoff Visibility
    print_status "INFO" "Test 3: Human Handoff Visibility"
    total_tests=$((total_tests + 1))
    if test_handoff_visibility; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 4: Agent Autonomy
    print_status "INFO" "Test 4: Agent Autonomous Decision Making"
    total_tests=$((total_tests + 1))
    if test_agent_autonomy; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 5: Queue System
    print_status "INFO" "Test 5: Queue System"
    total_tests=$((total_tests + 1))
    if test_queue_system; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 6: Monitoring
    print_status "INFO" "Test 6: Monitoring and Metrics"
    total_tests=$((total_tests + 1))
    if test_monitoring; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Summary
    echo ""
    echo "=============================================="
    print_status "INFO" "Verification Summary"
    echo "=============================================="
    print_status "INFO" "Total Tests: $total_tests"
    print_status "SUCCESS" "Passed: $passed_tests"
    print_status "FAILURE" "Failed: $failed_tests"
    
    local success_rate=$((passed_tests * 100 / total_tests))
    
    if [ "$failed_tests" -eq "0" ]; then
        print_status "SUCCESS" "All tests passed! System is fully operational."
        echo ""
        print_status "INFO" "🎉 CVLeap Agent System is ready for production use!"
        print_status "INFO" "📊 Dashboard: $DASHBOARD_URL"
        print_status "INFO" "🔧 Namespace: $NAMESPACE"
        exit 0
    elif [ "$success_rate" -ge "80" ]; then
        print_status "WARNING" "Most tests passed ($success_rate%). System is operational with minor issues."
        echo ""
        print_status "INFO" "⚠️  CVLeap Agent System is operational but may need attention."
        exit 1
    else
        print_status "FAILURE" "Multiple tests failed ($success_rate% success rate). System needs attention."
        echo ""
        print_status "FAILURE" "❌ CVLeap Agent System has significant issues and may not be ready for production."
        exit 2
    fi
}

# Handle script arguments
case "${1:-verify}" in
    "verify")
        main
        ;;
    "pods")
        test_pod_isolation
        ;;
    "handoff")
        test_handoff_visibility
        ;;
    "autonomy")
        test_agent_autonomy
        ;;
    "monitoring")
        test_monitoring
        ;;
    "queue")
        test_queue_system
        ;;
    "help")
        echo "Usage: $0 [verify|pods|handoff|autonomy|monitoring|queue|help]"
        echo ""
        echo "Commands:"
        echo "  verify    - Run all verification tests (default)"
        echo "  pods      - Test POD isolation only"
        echo "  handoff   - Test human handoff visibility only"
        echo "  autonomy  - Test agent autonomous decision making only"
        echo "  monitoring- Test monitoring stack only"
        echo "  queue     - Test queue system only"
        echo "  help      - Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac
