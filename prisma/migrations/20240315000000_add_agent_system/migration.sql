-- Create<PERSON><PERSON>
CREATE TYPE "AgentType" AS ENUM ('PLANNING', 'RESEARCH', 'EXECUTION', 'RESUME_OPTIMIZATION', 'MONITORING');

-- CreateEnum
CREATE TYPE "TaskStatus" AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "PipelineStatus" AS ENUM ('CREATED', 'RUNNING', 'PAUSED', 'COMPLETED', 'FAILED', 'CANCELLED');

-- Create<PERSON><PERSON>
CREATE TYPE "ApplicationStatusType" AS ENUM ('SUBMITTED', 'VIEWED', 'UNDER_REVIEW', 'INTERVIEW_SCHEDULED', 'REJECTED', 'OFFER_RECEIVED', 'WITHDRAWN');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "FollowUpActionType" AS ENUM ('FOLLOW_UP_EMAIL', 'LINKEDIN_MESSAGE', 'PHONE_CALL', 'APPLICATION_WITHDRAWAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ActionStatus" AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'SKIPPED');

-- CreateEnum
CREATE TYPE "EmailClassification" AS ENUM ('APPLICATION_CONFIRMATION', 'INTERVIEW_INVITATION', 'REJECTION', 'OFFER', 'FOLLOW_UP', 'OTHER');

-- CreateEnum
CREATE TYPE "Sentiment" AS ENUM ('POSITIVE', 'NEUTRAL', 'NEGATIVE');

-- CreateTable
CREATE TABLE "Agent" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "AgentType" NOT NULL,
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "capabilities" JSONB NOT NULL,
    "configuration" JSONB NOT NULL DEFAULT '{}',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastHeartbeat" TIMESTAMP(3),
    "resourceUsage" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AgentTask" (
    "id" TEXT NOT NULL,
    "agentId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "status" "TaskStatus" NOT NULL DEFAULT 'PENDING',
    "data" JSONB NOT NULL,
    "result" JSONB,
    "error" TEXT,
    "estimatedDuration" INTEGER,
    "actualDuration" INTEGER,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "maxRetries" INTEGER NOT NULL DEFAULT 3,
    "scheduledAt" TIMESTAMP(3),
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AgentTask_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Pipeline" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" "PipelineStatus" NOT NULL DEFAULT 'CREATED',
    "configuration" JSONB NOT NULL,
    "tasks" JSONB NOT NULL DEFAULT '[]',
    "progress" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "estimatedDuration" INTEGER,
    "actualDuration" INTEGER,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Pipeline_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApplicationStatusHistory" (
    "id" TEXT NOT NULL,
    "applicationId" TEXT NOT NULL,
    "status" "ApplicationStatusType" NOT NULL,
    "source" TEXT NOT NULL,
    "confidence" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "details" JSONB,
    "detectedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ApplicationStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FollowUpAction" (
    "id" TEXT NOT NULL,
    "applicationId" TEXT NOT NULL,
    "actionType" "FollowUpActionType" NOT NULL,
    "scheduledDate" TIMESTAMP(3) NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "template" TEXT,
    "status" "ActionStatus" NOT NULL DEFAULT 'PENDING',
    "executedAt" TIMESTAMP(3),
    "result" JSONB,
    "error" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FollowUpAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailInsight" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "applicationId" TEXT,
    "sender" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "receivedAt" TIMESTAMP(3) NOT NULL,
    "classification" "EmailClassification" NOT NULL,
    "sentiment" "Sentiment" NOT NULL,
    "actionRequired" BOOLEAN NOT NULL DEFAULT false,
    "extractedData" JSONB DEFAULT '{}',
    "confidence" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "EmailInsight_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OptimizedResume" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "baseResumeId" TEXT NOT NULL,
    "targetJobId" TEXT,
    "optimizedContent" TEXT NOT NULL,
    "optimizationScore" DOUBLE PRECISION NOT NULL,
    "atsScore" DOUBLE PRECISION NOT NULL,
    "keywordMatches" JSONB NOT NULL DEFAULT '{}',
    "improvements" JSONB NOT NULL DEFAULT '[]',
    "targetJob" JSONB NOT NULL DEFAULT '{}',
    "performanceMetrics" JSONB DEFAULT '{}',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OptimizedResume_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResumeVariant" (
    "id" TEXT NOT NULL,
    "baseResumeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "variantType" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "performanceMetrics" JSONB NOT NULL DEFAULT '{}',
    "testStatus" TEXT NOT NULL DEFAULT 'active',
    "testingStrategy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ResumeVariant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobMarketAnalysis" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "targetRoles" TEXT[],
    "location" TEXT[],
    "analysisType" TEXT NOT NULL,
    "trends" JSONB NOT NULL DEFAULT '[]',
    "insights" JSONB NOT NULL DEFAULT '{}',
    "recommendations" TEXT[],
    "dataSource" TEXT NOT NULL,
    "analysisDate" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JobMarketAnalysis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyResearch" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "industry" TEXT,
    "size" TEXT,
    "culture" JSONB DEFAULT '{}',
    "hiringTrends" JSONB DEFAULT '{}',
    "applicationTips" TEXT[],
    "recentNews" TEXT[],
    "researchDepth" TEXT NOT NULL DEFAULT 'basic',
    "confidence" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CompanyResearch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobOpportunity" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "company" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "remote" BOOLEAN NOT NULL DEFAULT false,
    "salary" JSONB,
    "description" TEXT NOT NULL,
    "requirements" TEXT[],
    "benefits" TEXT[],
    "applicationUrl" TEXT NOT NULL,
    "portalType" TEXT NOT NULL,
    "matchScore" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "postedDate" TIMESTAMP(3) NOT NULL,
    "discoveredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "applied" BOOLEAN NOT NULL DEFAULT false,
    "applicationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JobOpportunity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PerformanceMetrics" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "timeRangeStart" TIMESTAMP(3) NOT NULL,
    "timeRangeEnd" TIMESTAMP(3) NOT NULL,
    "totalApplications" INTEGER NOT NULL DEFAULT 0,
    "responseRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "interviewRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "offerRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "averageResponseTime" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "topPerformingResumes" TEXT[],
    "bestPerformingCompanies" TEXT[],
    "skillsInDemand" TEXT[],
    "trends" JSONB DEFAULT '{}',
    "recommendations" TEXT[],
    "calculatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PerformanceMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AgentMetrics" (
    "id" TEXT NOT NULL,
    "agentId" TEXT NOT NULL,
    "metricName" TEXT NOT NULL,
    "metricValue" DOUBLE PRECISION NOT NULL,
    "metricType" TEXT NOT NULL DEFAULT 'gauge',
    "labels" JSONB DEFAULT '{}',
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AgentMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Agent_type_idx" ON "Agent"("type");

-- CreateIndex
CREATE INDEX "Agent_isActive_idx" ON "Agent"("isActive");

-- CreateIndex
CREATE INDEX "AgentTask_agentId_idx" ON "AgentTask"("agentId");

-- CreateIndex
CREATE INDEX "AgentTask_userId_idx" ON "AgentTask"("userId");

-- CreateIndex
CREATE INDEX "AgentTask_status_idx" ON "AgentTask"("status");

-- CreateIndex
CREATE INDEX "AgentTask_scheduledAt_idx" ON "AgentTask"("scheduledAt");

-- CreateIndex
CREATE INDEX "Pipeline_userId_idx" ON "Pipeline"("userId");

-- CreateIndex
CREATE INDEX "Pipeline_status_idx" ON "Pipeline"("status");

-- CreateIndex
CREATE INDEX "ApplicationStatusHistory_applicationId_idx" ON "ApplicationStatusHistory"("applicationId");

-- CreateIndex
CREATE INDEX "ApplicationStatusHistory_status_idx" ON "ApplicationStatusHistory"("status");

-- CreateIndex
CREATE INDEX "FollowUpAction_applicationId_idx" ON "FollowUpAction"("applicationId");

-- CreateIndex
CREATE INDEX "FollowUpAction_scheduledDate_idx" ON "FollowUpAction"("scheduledDate");

-- CreateIndex
CREATE INDEX "FollowUpAction_status_idx" ON "FollowUpAction"("status");

-- CreateIndex
CREATE INDEX "EmailInsight_userId_idx" ON "EmailInsight"("userId");

-- CreateIndex
CREATE INDEX "EmailInsight_applicationId_idx" ON "EmailInsight"("applicationId");

-- CreateIndex
CREATE INDEX "EmailInsight_classification_idx" ON "EmailInsight"("classification");

-- CreateIndex
CREATE INDEX "EmailInsight_processed_idx" ON "EmailInsight"("processed");

-- CreateIndex
CREATE INDEX "OptimizedResume_userId_idx" ON "OptimizedResume"("userId");

-- CreateIndex
CREATE INDEX "OptimizedResume_baseResumeId_idx" ON "OptimizedResume"("baseResumeId");

-- CreateIndex
CREATE INDEX "OptimizedResume_isActive_idx" ON "OptimizedResume"("isActive");

-- CreateIndex
CREATE INDEX "ResumeVariant_baseResumeId_idx" ON "ResumeVariant"("baseResumeId");

-- CreateIndex
CREATE INDEX "ResumeVariant_userId_idx" ON "ResumeVariant"("userId");

-- CreateIndex
CREATE INDEX "JobMarketAnalysis_userId_idx" ON "JobMarketAnalysis"("userId");

-- CreateIndex
CREATE INDEX "JobMarketAnalysis_analysisDate_idx" ON "JobMarketAnalysis"("analysisDate");

-- CreateIndex
CREATE INDEX "CompanyResearch_userId_idx" ON "CompanyResearch"("userId");

-- CreateIndex
CREATE INDEX "CompanyResearch_companyName_idx" ON "CompanyResearch"("companyName");

-- CreateIndex
CREATE INDEX "JobOpportunity_userId_idx" ON "JobOpportunity"("userId");

-- CreateIndex
CREATE INDEX "JobOpportunity_company_idx" ON "JobOpportunity"("company");

-- CreateIndex
CREATE INDEX "JobOpportunity_matchScore_idx" ON "JobOpportunity"("matchScore");

-- CreateIndex
CREATE INDEX "JobOpportunity_applied_idx" ON "JobOpportunity"("applied");

-- CreateIndex
CREATE INDEX "PerformanceMetrics_userId_idx" ON "PerformanceMetrics"("userId");

-- CreateIndex
CREATE INDEX "PerformanceMetrics_timeRangeStart_idx" ON "PerformanceMetrics"("timeRangeStart");

-- CreateIndex
CREATE INDEX "AgentMetrics_agentId_idx" ON "AgentMetrics"("agentId");

-- CreateIndex
CREATE INDEX "AgentMetrics_metricName_idx" ON "AgentMetrics"("metricName");

-- CreateIndex
CREATE INDEX "AgentMetrics_timestamp_idx" ON "AgentMetrics"("timestamp");

-- AddForeignKey
ALTER TABLE "AgentTask" ADD CONSTRAINT "AgentTask_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AgentTask" ADD CONSTRAINT "AgentTask_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Pipeline" ADD CONSTRAINT "Pipeline_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApplicationStatusHistory" ADD CONSTRAINT "ApplicationStatusHistory_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "JobApplication"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowUpAction" ADD CONSTRAINT "FollowUpAction_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "JobApplication"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailInsight" ADD CONSTRAINT "EmailInsight_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailInsight" ADD CONSTRAINT "EmailInsight_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "JobApplication"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OptimizedResume" ADD CONSTRAINT "OptimizedResume_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OptimizedResume" ADD CONSTRAINT "OptimizedResume_baseResumeId_fkey" FOREIGN KEY ("baseResumeId") REFERENCES "Resume"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OptimizedResume" ADD CONSTRAINT "OptimizedResume_targetJobId_fkey" FOREIGN KEY ("targetJobId") REFERENCES "Job"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResumeVariant" ADD CONSTRAINT "ResumeVariant_baseResumeId_fkey" FOREIGN KEY ("baseResumeId") REFERENCES "Resume"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResumeVariant" ADD CONSTRAINT "ResumeVariant_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobMarketAnalysis" ADD CONSTRAINT "JobMarketAnalysis_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyResearch" ADD CONSTRAINT "CompanyResearch_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobOpportunity" ADD CONSTRAINT "JobOpportunity_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobOpportunity" ADD CONSTRAINT "JobOpportunity_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "JobApplication"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PerformanceMetrics" ADD CONSTRAINT "PerformanceMetrics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AgentMetrics" ADD CONSTRAINT "AgentMetrics_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;
