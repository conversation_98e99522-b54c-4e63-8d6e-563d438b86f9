import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { Redis } from 'ioredis';
import { createLogger } from 'winston';
import { AgentOrchestrator } from '../../src/agents/core/agent-orchestrator';
import { PlanningAgent } from '../../src/agents/specialized/planning-agent';
import { ResearchAgent } from '../../src/agents/specialized/research-agent';
import { ExecutionAgent } from '../../src/agents/specialized/execution-agent';
import { ResumeOptimizationAgent } from '../../src/agents/specialized/resume-optimization-agent';
import { MonitoringAgent } from '../../src/agents/specialized/monitoring-agent';
import { AIDecisionEngine } from '../../src/lib/ai-decision-engine';
import { HumanHandoffManager } from '../../src/lib/human-handoff-manager';
import { RedisQueue } from '../../src/lib/redis-queue';

describe('CVLeap Agent System Integration Tests', () => {
  let redis: Redis;
  let logger: any;
  let orchestrator: AgentOrchestrator;
  let planningAgent: PlanningAgent;
  let researchAgent: ResearchAgent;
  let executionAgent: ExecutionAgent;
  let resumeAgent: ResumeOptimizationAgent;
  let monitoringAgent: MonitoringAgent;
  let decisionEngine: AIDecisionEngine;
  let handoffManager: HumanHandoffManager;
  let taskQueue: RedisQueue;

  beforeAll(async () => {
    // Setup test environment
    redis = new Redis(process.env.REDIS_TEST_URL || 'redis://localhost:6379/1');
    logger = createLogger({
      level: 'error', // Reduce noise in tests
      silent: true
    });

    // Initialize components
    orchestrator = new AgentOrchestrator(redis, logger);
    planningAgent = new PlanningAgent(redis, logger);
    researchAgent = new ResearchAgent(redis, logger);
    executionAgent = new ExecutionAgent(redis, logger);
    resumeAgent = new ResumeOptimizationAgent(redis, logger);
    monitoringAgent = new MonitoringAgent(redis, logger);
    decisionEngine = new AIDecisionEngine(logger);
    handoffManager = new HumanHandoffManager(redis, logger);
    taskQueue = new RedisQueue(redis, logger, 'test-queue');

    // Register agents
    await orchestrator.registerAgent(planningAgent);
    await orchestrator.registerAgent(researchAgent);
    await orchestrator.registerAgent(executionAgent);
    await orchestrator.registerAgent(resumeAgent);
    await orchestrator.registerAgent(monitoringAgent);

    await orchestrator.start();
  });

  afterAll(async () => {
    await orchestrator.stop();
    await redis.flushdb(); // Clean test database
    await redis.disconnect();
  });

  beforeEach(async () => {
    await redis.flushdb(); // Clean state between tests
  });

  describe('Agent Autonomy Tests', () => {
    test('Planning Agent should make autonomous decisions about pipeline structure', async () => {
      const jobSearchGoal = {
        userId: 'test-user-1',
        targetRoles: ['Software Engineer', 'Full Stack Developer'],
        targetCompanies: ['Google', 'Microsoft', 'Apple'],
        salaryRange: { min: 120000, max: 180000 },
        location: ['San Francisco', 'Seattle'],
        remote: true,
        timeline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        preferences: {
          industryPreferences: ['Technology', 'Fintech'],
          companySizePreference: 'large' as const,
          workLifeBalance: 8,
          careerGrowth: 9
        }
      };

      // Test autonomous decision making
      const decision = await decisionEngine.makeDecision({
        situation: 'User has aggressive timeline and high salary expectations',
        availableActions: ['conservative_approach', 'aggressive_approach', 'balanced_approach'],
        constraints: { timeline: 30, budget: 'unlimited' },
        urgency: 'high',
        riskTolerance: 'moderate'
      });

      expect(decision.action).toBeDefined();
      expect(decision.confidence).toBeGreaterThan(0);
      expect(decision.reasoning).toContain('timeline');
      expect(decision.requiresHumanApproval).toBeDefined();

      // Test pipeline creation with decision
      const message = {
        id: 'test-msg-1',
        type: 'new_job_search_goal',
        payload: { goal: jobSearchGoal },
        sender: 'test-system',
        timestamp: new Date(),
        correlationId: 'test-correlation-1'
      };

      await planningAgent.handleMessage(message);

      // Verify autonomous behavior
      const stats = await orchestrator.getAgentStats('PlanningAgent');
      expect(stats.tasksCreated).toBeGreaterThan(0);
    });

    test('Research Agent should adapt scraping strategies autonomously', async () => {
      const researchTask = {
        id: 'research-task-1',
        type: 'job_discovery',
        data: {
          searchCriteria: {
            roles: ['Software Engineer'],
            location: ['Remote'],
            remote: true
          },
          maxResults: 10,
          platforms: ['linkedin', 'indeed']
        },
        priority: 5,
        status: 'pending' as const,
        createdAt: new Date(),
        estimatedDuration: 300000
      };

      // Simulate portal failure to test adaptability
      const mockPortalFailure = {
        situation: 'LinkedIn portal blocking requests',
        availableActions: ['switch_to_indeed', 'use_proxy', 'wait_and_retry', 'human_intervention'],
        constraints: { timeLimit: 600000, maxRetries: 3 },
        urgency: 'medium' as const
      };

      const adaptationDecision = await decisionEngine.makeDecision(mockPortalFailure);
      
      expect(adaptationDecision.action).toBeDefined();
      expect(['switch_to_indeed', 'use_proxy', 'wait_and_retry'].includes(adaptationDecision.action)).toBe(true);
      expect(adaptationDecision.confidence).toBeGreaterThan(0.3);
    });
  });

  describe('Agent Reactivity Tests', () => {
    test('Execution Agent should react to portal changes', async () => {
      const portalChangeScenario = {
        situation: 'Job portal updated form structure, previous selectors failing',
        availableActions: ['analyze_new_structure', 'use_fallback_method', 'request_human_help'],
        constraints: { maxAnalysisTime: 120000, applicationDeadline: new Date(Date.now() + 3600000) },
        urgency: 'high' as const
      };

      const reactionDecision = await decisionEngine.makeDecision(portalChangeScenario);
      
      expect(reactionDecision.action).toBeDefined();
      expect(reactionDecision.confidence).toBeGreaterThan(0);
      
      // High urgency should prefer quick solutions
      if (reactionDecision.confidence > 0.7) {
        expect(['analyze_new_structure', 'use_fallback_method'].includes(reactionDecision.action)).toBe(true);
      }
    });

    test('Monitoring Agent should react to application status changes', async () => {
      const statusChangeScenario = {
        situation: 'Application status changed to "Interview Scheduled"',
        availableActions: ['prepare_interview_materials', 'research_interviewers', 'schedule_prep_session', 'notify_user'],
        constraints: { interviewDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) },
        urgency: 'medium' as const
      };

      const reactionDecision = await decisionEngine.makeDecision(statusChangeScenario);
      
      expect(reactionDecision.action).toBeDefined();
      expect(reactionDecision.alternatives.length).toBeGreaterThan(0);
    });
  });

  describe('Agent Learning Tests', () => {
    test('Resume Optimization Agent should learn from feedback', async () => {
      const optimizationContext = {
        situation: 'Resume optimization for Software Engineer role at tech startup',
        availableActions: ['emphasize_startup_experience', 'highlight_technical_skills', 'focus_on_leadership'],
        constraints: { atsCompatibility: true, maxLength: 2 }
      };

      // First decision
      const initialDecision = await decisionEngine.makeDecision(optimizationContext);
      const initialConfidence = initialDecision.confidence;

      // Simulate positive feedback
      await decisionEngine.learnFromOutcome(
        optimizationContext,
        initialDecision,
        'success',
        'Resume resulted in interview invitation'
      );

      // Second similar decision should have higher confidence
      const secondDecision = await decisionEngine.makeDecision(optimizationContext);
      
      // Learning should improve confidence for successful patterns
      if (initialDecision.action === secondDecision.action) {
        expect(secondDecision.confidence).toBeGreaterThanOrEqual(initialConfidence);
      }

      const stats = decisionEngine.getDecisionStats();
      expect(stats.totalDecisions).toBeGreaterThan(0);
      expect(stats.learnedPatterns).toBeGreaterThan(0);
    });
  });

  describe('Human Handoff Integration Tests', () => {
    test('Should trigger human handoff for low confidence decisions', async () => {
      const complexScenario = {
        situation: 'Unusual job application requiring custom cover letter for CEO position',
        availableActions: ['use_template', 'write_custom', 'research_ceo_background'],
        constraints: { deadline: new Date(Date.now() + 3600000) },
        urgency: 'high' as const,
        riskTolerance: 'conservative' as const
      };

      const decision = await decisionEngine.makeDecision(complexScenario);
      
      if (decision.requiresHumanApproval) {
        const handoffRequest = await handoffManager.requestHumanApproval(
          'execution-agent-1',
          'execution',
          'test-user-1',
          complexScenario,
          decision,
          'Low confidence decision for high-stakes application'
        );

        expect(handoffRequest.id).toBeDefined();
        expect(handoffRequest.status).toBe('pending');
        expect(handoffRequest.priority).toBeDefined();

        // Test handoff visibility
        const pendingHandoffs = await handoffManager.getPendingHandoffs('test-user-1');
        expect(pendingHandoffs.length).toBeGreaterThan(0);
        expect(pendingHandoffs[0].id).toBe(handoffRequest.id);
      }
    });

    test('Should provide complete visibility into agent decisions', async () => {
      const decision = await decisionEngine.makeDecision({
        situation: 'Standard job application to well-known company',
        availableActions: ['apply_immediately', 'customize_resume_first', 'research_company_more'],
        constraints: { timeAvailable: 1800000 }
      });

      expect(decision.reasoning).toBeDefined();
      expect(decision.reasoning.length).toBeGreaterThan(10);
      expect(decision.metadata).toBeDefined();
      expect(decision.metadata.source).toBeDefined();
    });
  });

  describe('Queue and Message Routing Tests', () => {
    test('Should handle priority-based task queuing', async () => {
      // Enqueue tasks with different priorities
      await taskQueue.enqueue('urgent_task', { data: 'urgent' }, { priority: 'urgent' });
      await taskQueue.enqueue('low_task', { data: 'low' }, { priority: 'low' });
      await taskQueue.enqueue('high_task', { data: 'high' }, { priority: 'high' });

      // Dequeue should return highest priority first
      const firstTask = await taskQueue.dequeue();
      expect(firstTask?.type).toBe('urgent_task');

      const secondTask = await taskQueue.dequeue();
      expect(secondTask?.type).toBe('high_task');

      const thirdTask = await taskQueue.dequeue();
      expect(thirdTask?.type).toBe('low_task');
    });

    test('Should handle task retry logic', async () => {
      const taskId = await taskQueue.enqueue('failing_task', { data: 'test' });
      const task = await taskQueue.dequeue();
      
      expect(task?.id).toBe(taskId);
      
      // Simulate failure
      await taskQueue.nack(taskId, 'Simulated failure');
      
      // Task should be requeued
      const stats = await taskQueue.getQueueStats();
      expect(stats.pending.medium).toBeGreaterThan(0);
    });
  });

  describe('End-to-End Pipeline Tests', () => {
    test('Should execute complete job application pipeline', async () => {
      const pipelineId = 'test-pipeline-1';
      const userId = 'test-user-1';

      // Create pipeline
      const pipeline = await orchestrator.createPipeline({
        id: pipelineId,
        name: 'Test Job Application Pipeline',
        userId,
        steps: [
          {
            agent: 'research',
            task: 'job_discovery',
            data: {
              searchCriteria: {
                roles: ['Software Engineer'],
                location: ['Remote'],
                remote: true
              },
              maxResults: 5,
              platforms: ['linkedin']
            }
          },
          {
            agent: 'resume-optimization',
            task: 'resume_optimization',
            data: {
              resumeId: 'test-resume-1',
              targetRole: 'Software Engineer'
            }
          }
        ]
      });

      expect(pipeline.id).toBe(pipelineId);
      expect(pipeline.status).toBe('created');

      // Start pipeline execution
      await orchestrator.startPipeline(pipelineId);

      // Wait for some processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check pipeline progress
      const status = await orchestrator.getPipelineStatus(pipelineId);
      expect(['running', 'completed', 'failed'].includes(status.status)).toBe(true);
    }, 10000); // 10 second timeout for integration test
  });

  describe('Performance and Scaling Tests', () => {
    test('Should handle concurrent agent operations', async () => {
      const concurrentTasks = Array.from({ length: 10 }, (_, i) => 
        taskQueue.enqueue(`concurrent_task_${i}`, { index: i }, { priority: 'medium' })
      );

      const taskIds = await Promise.all(concurrentTasks);
      expect(taskIds.length).toBe(10);

      // Process tasks concurrently
      const processingPromises = Array.from({ length: 5 }, async () => {
        const task = await taskQueue.dequeue();
        if (task) {
          await taskQueue.ack(task.id);
        }
        return task;
      });

      const processedTasks = await Promise.all(processingPromises);
      const successfulTasks = processedTasks.filter(task => task !== null);
      expect(successfulTasks.length).toBeGreaterThan(0);
    });

    test('Should maintain performance under load', async () => {
      const startTime = Date.now();
      
      // Create multiple decisions rapidly
      const decisions = await Promise.all(
        Array.from({ length: 20 }, () =>
          decisionEngine.makeDecision({
            situation: 'Load test scenario',
            availableActions: ['action1', 'action2', 'action3'],
            constraints: {}
          })
        )
      );

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      expect(decisions.length).toBe(20);
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
      
      // All decisions should be valid
      decisions.forEach(decision => {
        expect(decision.action).toBeDefined();
        expect(decision.confidence).toBeGreaterThan(0);
      });
    });
  });
});
