import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/route';
import { prisma } from '../../../../../lib/db';

const AGENT_SERVICE_URL = process.env.AGENT_SERVICE_URL || 'http://localhost:8080';

interface RouteParams {
  params: {
    agentType: string;
    taskId: string;
  };
}

// GET /api/agents/[agentType]/tasks/[taskId] - Get task status
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { agentType, taskId } = params;

    // Verify task belongs to user
    const task = await prisma.agentTask.findFirst({
      where: {
        id: taskId,
        userId: session.user.id,
      },
      include: {
        agent: true,
      },
    });

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Get latest status from agent service
    const response = await fetch(`${AGENT_SERVICE_URL}/agents/${agentType}/tasks/${taskId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const agentTaskData = await response.json();
      
      // Update local database if status changed
      if (agentTaskData.status !== task.status) {
        await prisma.agentTask.update({
          where: { id: taskId },
          data: {
            status: agentTaskData.status,
            result: agentTaskData.result,
            error: agentTaskData.error,
            actualDuration: agentTaskData.actualDuration,
            completedAt: agentTaskData.completedAt ? new Date(agentTaskData.completedAt) : null,
          },
        });
      }

      return NextResponse.json({
        ...task,
        ...agentTaskData,
      });
    } else {
      // Return database version if agent service is unavailable
      return NextResponse.json(task);
    }

  } catch (error) {
    console.error('Failed to get task status:', error);
    return NextResponse.json(
      { error: 'Failed to get task status' },
      { status: 500 }
    );
  }
}

// DELETE /api/agents/[agentType]/tasks/[taskId] - Cancel task
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { agentType, taskId } = params;

    // Verify task belongs to user
    const task = await prisma.agentTask.findFirst({
      where: {
        id: taskId,
        userId: session.user.id,
      },
    });

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Cancel task in agent service
    const response = await fetch(`${AGENT_SERVICE_URL}/agents/${agentType}/tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Update local database regardless of agent service response
    await prisma.agentTask.update({
      where: { id: taskId },
      data: {
        status: 'CANCELLED',
        completedAt: new Date(),
      },
    });

    if (!response.ok) {
      console.warn(`Agent service cancel failed for task ${taskId}, but local status updated`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Failed to cancel task:', error);
    return NextResponse.json(
      { error: 'Failed to cancel task' },
      { status: 500 }
    );
  }
}

// PATCH /api/agents/[agentType]/tasks/[taskId] - Update task
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { agentType, taskId } = params;
    const body = await request.json();

    // Verify task belongs to user
    const task = await prisma.agentTask.findFirst({
      where: {
        id: taskId,
        userId: session.user.id,
      },
    });

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Update task in database
    const updatedTask = await prisma.agentTask.update({
      where: { id: taskId },
      data: {
        ...body,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedTask);

  } catch (error) {
    console.error('Failed to update task:', error);
    return NextResponse.json(
      { error: 'Failed to update task' },
      { status: 500 }
    );
  }
}
