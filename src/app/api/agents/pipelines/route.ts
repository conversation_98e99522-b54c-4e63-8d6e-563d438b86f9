import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { prisma } from '../../../lib/db';

const AGENT_SERVICE_URL = process.env.AGENT_SERVICE_URL || 'http://localhost:8080';

// GET /api/agents/pipelines - Get user's pipelines
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const whereClause: any = {
      userId: session.user.id,
    };

    if (status) {
      whereClause.status = status.toUpperCase();
    }

    const pipelines = await prisma.pipeline.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      skip: offset,
    });

    const total = await prisma.pipeline.count({
      where: whereClause,
    });

    // Get latest status from agent service for active pipelines
    const activePipelines = pipelines.filter(p => 
      ['CREATED', 'RUNNING', 'PAUSED'].includes(p.status)
    );

    const updatedPipelines = await Promise.all(
      pipelines.map(async (pipeline) => {
        if (activePipelines.includes(pipeline)) {
          try {
            const response = await fetch(`${AGENT_SERVICE_URL}/orchestrator/pipelines/${pipeline.id}`);
            if (response.ok) {
              const agentData = await response.json();
              
              // Update database if status changed
              if (agentData.status !== pipeline.status) {
                await prisma.pipeline.update({
                  where: { id: pipeline.id },
                  data: {
                    status: agentData.status,
                    progress: agentData.progress,
                    actualDuration: agentData.actualDuration,
                    completedAt: agentData.completedAt ? new Date(agentData.completedAt) : null,
                  },
                });
              }

              return { ...pipeline, ...agentData };
            }
          } catch (error) {
            console.warn(`Failed to get pipeline status for ${pipeline.id}:`, error);
          }
        }
        return pipeline;
      })
    );

    return NextResponse.json({
      pipelines: updatedPipelines,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
    });

  } catch (error) {
    console.error('Failed to get pipelines:', error);
    return NextResponse.json(
      { error: 'Failed to get pipelines' },
      { status: 500 }
    );
  }
}

// POST /api/agents/pipelines - Create a new pipeline
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, type, configuration } = body;

    // Validate pipeline type
    const validTypes = ['job_search', 'resume_optimization', 'market_analysis', 'application_automation'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: `Invalid pipeline type. Must be one of: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Create pipeline configuration based on type
    let pipelineConfig;
    switch (type) {
      case 'job_search':
        pipelineConfig = {
          name,
          description,
          userId: session.user.id,
          steps: [
            {
              agent: 'research',
              task: 'job_discovery',
              data: configuration.searchCriteria,
            },
            {
              agent: 'resume-optimization',
              task: 'resume_optimization',
              data: configuration.resumeOptimization,
            },
            {
              agent: 'execution',
              task: 'batch_application',
              data: configuration.applicationSettings,
            },
            {
              agent: 'monitoring',
              task: 'application_monitoring',
              data: configuration.monitoringSettings,
            },
          ],
        };
        break;

      case 'resume_optimization':
        pipelineConfig = {
          name,
          description,
          userId: session.user.id,
          steps: [
            {
              agent: 'research',
              task: 'market_analysis',
              data: configuration.marketAnalysis,
            },
            {
              agent: 'resume-optimization',
              task: 'resume_optimization',
              data: configuration.optimization,
            },
            {
              agent: 'resume-optimization',
              task: 'resume_variant_generation',
              data: configuration.variants,
            },
          ],
        };
        break;

      case 'market_analysis':
        pipelineConfig = {
          name,
          description,
          userId: session.user.id,
          steps: [
            {
              agent: 'research',
              task: 'market_analysis',
              data: configuration.analysis,
            },
            {
              agent: 'research',
              task: 'company_research',
              data: configuration.companies,
            },
          ],
        };
        break;

      case 'application_automation':
        pipelineConfig = {
          name,
          description,
          userId: session.user.id,
          steps: [
            {
              agent: 'planning',
              task: 'create_application_plan',
              data: configuration.planning,
            },
            {
              agent: 'execution',
              task: 'batch_application',
              data: configuration.execution,
            },
            {
              agent: 'monitoring',
              task: 'application_monitoring',
              data: configuration.monitoring,
            },
          ],
        };
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid pipeline type' },
          { status: 400 }
        );
    }

    // Create pipeline in agent service
    const response = await fetch(`${AGENT_SERVICE_URL}/orchestrator/pipelines`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pipelineConfig),
    });

    if (!response.ok) {
      throw new Error(`Agent service responded with status: ${response.status}`);
    }

    const agentPipeline = await response.json();

    // Store pipeline in database
    const pipeline = await prisma.pipeline.create({
      data: {
        id: agentPipeline.id,
        userId: session.user.id,
        name,
        description,
        configuration: pipelineConfig,
        tasks: agentPipeline.tasks || [],
        estimatedDuration: agentPipeline.estimatedDuration,
      },
    });

    return NextResponse.json({
      pipelineId: pipeline.id,
      status: pipeline.status,
      estimatedDuration: pipeline.estimatedDuration,
      tasks: pipeline.tasks,
    });

  } catch (error) {
    console.error('Failed to create pipeline:', error);
    return NextResponse.json(
      { error: 'Failed to create pipeline' },
      { status: 500 }
    );
  }
}
