import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { prisma } from '../../../lib/db';
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
const AGENT_SERVICE_URL = process.env.AGENT_SERVICE_URL || 'http://localhost:8080';

// GET /api/agents - Get agent status and capabilities
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch agent status from agent service
    const response = await fetch(`${AGENT_SERVICE_URL}/agents`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Agent service responded with status: ${response.status}`);
    }

    const agentData = await response.json();

    // Get user's recent tasks
    const recentTasks = await prisma.agentTask.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
      include: {
        agent: true,
      },
    });

    // Get user's active pipelines
    const activePipelines = await prisma.pipeline.findMany({
      where: {
        userId: session.user.id,
        status: {
          in: ['CREATED', 'RUNNING', 'PAUSED'],
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({
      agents: agentData.agents,
      orchestrator: agentData.orchestrator,
      userActivity: {
        recentTasks,
        activePipelines,
      },
    });

  } catch (error) {
    console.error('Failed to get agent status:', error);
    return NextResponse.json(
      { error: 'Failed to get agent status' },
      { status: 500 }
    );
  }
}

// POST /api/agents - Create a new agent task or pipeline
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, agentType, taskType, data, priority = 'medium' } = body;

    if (type === 'task') {
      // Create individual agent task
      const response = await fetch(`${AGENT_SERVICE_URL}/agents/${agentType}/tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskType,
          data,
          priority,
          userId: session.user.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create task: ${response.status}`);
      }

      const taskResult = await response.json();

      // Store task in database
      const task = await prisma.agentTask.create({
        data: {
          id: taskResult.taskId,
          agentId: agentType,
          userId: session.user.id,
          type: taskType,
          priority,
          status: 'PENDING',
          data,
          estimatedDuration: taskResult.estimatedDuration,
        },
      });

      return NextResponse.json({
        taskId: task.id,
        status: task.status,
        estimatedDuration: task.estimatedDuration,
      });

    } else if (type === 'pipeline') {
      // Create pipeline
      const response = await fetch(`${AGENT_SERVICE_URL}/orchestrator/pipelines`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          userId: session.user.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create pipeline: ${response.status}`);
      }

      const pipelineResult = await response.json();

      // Store pipeline in database
      const pipeline = await prisma.pipeline.create({
        data: {
          id: pipelineResult.id,
          userId: session.user.id,
          name: data.name,
          description: data.description,
          configuration: data,
          tasks: pipelineResult.tasks || [],
          estimatedDuration: pipelineResult.estimatedDuration,
        },
      });

      return NextResponse.json({
        pipelineId: pipeline.id,
        status: pipeline.status,
        estimatedDuration: pipeline.estimatedDuration,
      });

    } else {
      return NextResponse.json(
        { error: 'Invalid request type. Must be "task" or "pipeline"' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Failed to create agent task/pipeline:', error);
    return NextResponse.json(
      { error: 'Failed to create agent task/pipeline' },
      { status: 500 }
    );
  }
}
