import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Award, BookOpen, Star, CheckCircle } from 'lucide-react';

interface CleanBlueAccentsTemplateProps {
  resumeData: ResumeContent;
}

export default function CleanBlueAccentsTemplate({ resumeData }: CleanBlueAccentsTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // <PERSON>ck references data
  const references = [
    { name: '<PERSON>', title: 'Senior Manager', company: 'ABC Corporation', phone: '+****************' },
    { name: '<PERSON>', title: 'Team Lead', company: 'XYZ Company', phone: '+****************' }
  ];

  // Mock certificates
  const certificates = [
    'Professional Certification in Project Management',
    'Advanced Excel and Data Analysis',
    'Digital Marketing Fundamentals',
    'Leadership and Team Management'
  ];

  // Mock languages
  const languages = [
    { name: 'English', level: 'Native' },
    { name: 'Spanish', level: 'Fluent' },
    { name: 'French', level: 'Intermediate' },
    { name: 'German', level: 'Basic' }
  ];

  // Mock projects if none provided
  const projectsList = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Digital Transformation Initiative', description: 'Led company-wide digital transformation project' },
    { id: '2', name: 'Customer Experience Optimization', description: 'Improved customer satisfaction by 40% through process redesign' },
    { id: '3', name: 'Data Analytics Platform', description: 'Implemented business intelligence solution for decision making' }
  ];

  return (
    <div 
      className="bg-[#f9fafb] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="jone-don-resume"
      style={{ fontFamily: 'Avenir, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-white shadow-sm border-b-2 border-[#001acc] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-[#101214] mb-1">
              {personalInfo.fullName || 'Jone Don'}
            </h1>
            <p className="text-lg text-[#001acc] font-medium">
              {personalInfo.jobTitle || 'Professional'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-[#101214] text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#001acc]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#001acc]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#001acc]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#001acc] pb-2">
              Profile
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#001acc]">
              <p className="text-sm text-[#101214] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#001acc] pb-2">
                Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#001acc]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#101214] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#001acc] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#001acc] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#101214] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <CheckCircle className="w-3 h-3 mr-2 mt-1 text-[#001acc] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f9fafb] text-[#001acc] px-2 py-1 rounded border border-[#001acc]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Projects */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#001acc] pb-2">
                Projects
              </h2>
              <div className="space-y-4">
                {projectsList.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#001acc]">
                    <h3 className="font-bold text-[#101214] text-sm flex items-center">
                      <Star className="w-4 h-4 mr-2 text-[#001acc]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#101214]/80 mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* References */}
            <div>
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#001acc] pb-2">
                References
              </h2>
              <div className="grid grid-cols-2 gap-4">
                {references.map((ref, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-[#001acc]/20">
                    <h3 className="font-bold text-sm text-[#101214]">
                      {ref.name}
                    </h3>
                    <p className="text-xs text-[#001acc] font-medium">
                      {ref.title}
                    </p>
                    <p className="text-xs text-[#101214]/70">
                      {ref.company}
                    </p>
                    <p className="text-xs text-[#101214]/70">
                      {ref.phone}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills, Education, Certificates, Languages */}
          <div className="col-span-1">
            
            {/* Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#001acc]/20">
                <div className="space-y-2">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 bg-[#001acc] rounded-full mr-2 flex-shrink-0"></div>
                      <span className="text-sm text-[#101214]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-[#001acc]/20">
                    <h3 className="font-bold text-sm text-[#101214] flex items-center">
                      <BookOpen className="w-4 h-4 mr-2 text-[#001acc]" />
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#001acc] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#101214]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#101214]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Certificates */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Certificates
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#001acc]/20">
                <div className="space-y-2">
                  {certificates.map((cert, index) => (
                    <div key={index} className="flex items-start">
                      <Award className="w-3 h-3 mr-2 mt-0.5 text-[#001acc] flex-shrink-0" />
                      <span className="text-xs text-[#101214] leading-tight">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Languages */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Languages
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#001acc]/20">
                <div className="space-y-2">
                  {languages.map((lang, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm text-[#101214]">{lang.name}</span>
                      <span className="text-xs text-[#001acc] font-medium">{lang.level}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#001acc]/20">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#001acc]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#001acc]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  {personalInfo.githubUrl && (
                    <div className="flex items-center text-sm text-[#001acc]">
                      <Github className="w-4 h-4 mr-2" />
                      <span>GitHub</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
