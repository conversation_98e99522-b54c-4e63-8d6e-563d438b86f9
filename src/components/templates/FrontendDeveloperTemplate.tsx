import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Code, Monitor, Smartphone, Terminal } from 'lucide-react';

interface FrontendDeveloperTemplateProps {
  resumeData: ResumeContent;
}

export default function FrontendDeveloperTemplate({ resumeData }: FrontendDeveloperTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock frontend projects if none provided
  const frontendProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'E-commerce React App', description: 'Built responsive shopping platform with Redux and TypeScript' },
    { id: '2', name: 'Dashboard Analytics Tool', description: 'Created data visualization dashboard using D3.js and React' },
    { id: '3', name: 'Mobile-First Portfolio', description: 'Developed responsive portfolio site with Next.js and Tailwind CSS' }
  ];

  return (
    <div 
      className="bg-[#f9fafb] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="aarav-mehta-frontend"
      style={{ fontFamily: 'Arial, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-white shadow-sm border-b border-[#101214]/10 px-8 py-6">
        <div className="flex items-center h-full">
          {/* Profile Photo */}
          <div className="w-20 h-20 rounded-full bg-[#73808d] border-3 border-[#f9fafb] overflow-hidden mr-6 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#73808d] to-[#101214] flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-[#101214] mb-2">
              {personalInfo.fullName || 'Aarav Mehta'}
            </h1>
            <p className="text-lg text-[#73808d] flex items-center font-medium">
              <Code className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Frontend Developer'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-[#101214] text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#73808d]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#73808d]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#73808d]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] px-8 py-6">
        
        {/* Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#73808d] pb-2">
              Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#73808d]">
              <p className="text-sm text-[#101214] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#73808d] pb-2">
                Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#73808d]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#101214] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#73808d] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#73808d] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#101214] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <Terminal className="w-3 h-3 mr-2 mt-1 text-[#73808d] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f9fafb] text-[#101214] px-2 py-1 rounded border border-[#73808d]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Projects */}
            <div>
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#73808d] pb-2">
                Projects
              </h2>
              <div className="space-y-4">
                {frontendProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#73808d]">
                    <h3 className="font-bold text-[#101214] text-sm flex items-center">
                      <Monitor className="w-4 h-4 mr-2 text-[#73808d]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#73808d] mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills & Education */}
          <div className="col-span-1">
            
            {/* Technical Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#73808d]/20">
                <div className="space-y-2">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <Code className="w-3 h-3 mr-2 text-[#73808d]" />
                      <span className="text-sm text-[#101214]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Technologies */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Technologies
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#73808d]/20">
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'React',
                    'Vue.js',
                    'Angular',
                    'TypeScript',
                    'Node.js',
                    'Next.js'
                  ].map((tech, index) => (
                    <div key={index} className="text-center py-2 bg-[#f9fafb] rounded border border-[#73808d]/20">
                      <span className="text-sm font-medium text-[#101214]">{tech}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-[#73808d]/20">
                    <h3 className="font-bold text-sm text-[#101214]">
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#73808d] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#101214]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#101214]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Tools & Frameworks */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Tools
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#73808d]/20">
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'VS Code',
                    'Git',
                    'Webpack',
                    'Docker',
                    'Jest',
                    'Figma'
                  ].map((tool, index) => (
                    <div key={index} className="text-center py-1 bg-[#f9fafb] rounded border border-[#73808d]/20">
                      <span className="text-xs font-medium text-[#101214]">{tool}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#73808d]/20">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#73808d]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#73808d]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  {personalInfo.githubUrl && (
                    <div className="flex items-center text-sm text-[#73808d]">
                      <Github className="w-4 h-4 mr-2" />
                      <span>GitHub</span>
                    </div>
                  )}
                  <div className="flex items-center text-sm text-[#73808d]">
                    <Smartphone className="w-4 h-4 mr-2" />
                    <span>CodePen</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
