import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, BarChart3, Target, Award, TrendingUp } from 'lucide-react';

interface BusinessAnalystPhotoTemplateProps {
  resumeData: ResumeContent;
}

export default function BusinessAnalystPhotoTemplate({ resumeData }: BusinessAnalystPhotoTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock business analysis projects if none provided
  const analysisProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Process Optimization Analysis', description: 'Identified inefficiencies resulting in 30% cost reduction' },
    { id: '2', name: 'Market Research & Strategy', description: 'Conducted comprehensive market analysis for new product launch' },
    { id: '3', name: 'Data-Driven Decision Support', description: 'Developed analytics framework for executive decision making' }
  ];

  return (
    <div 
      className="bg-[#f3f4f6] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="priya-kapoor-analyst"
      style={{ fontFamily: 'IBM Plex Sans, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-white shadow-sm border-b border-[#6b7280]/20 px-8 py-6">
        <div className="flex items-center h-full">
          {/* Profile Photo */}
          <div className="w-20 h-20 rounded-full bg-[#212121] border-3 border-[#f3f4f6] overflow-hidden mr-6 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#212121] to-[#6b7280] flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-[#212121] mb-2">
              {personalInfo.fullName || 'Priya Kapoor'}
            </h1>
            <p className="text-lg text-[#6b7280] flex items-center font-medium">
              <BarChart3 className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Business Analyst'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-[#212121] text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] px-8 py-6">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#212121] mb-4 border-b-2 border-[#6b7280] pb-2">
              Professional Profile
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#212121]">
              <p className="text-sm text-[#212121] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#212121] mb-4 border-b-2 border-[#6b7280] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#6b7280]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#212121] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#212121] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#6b7280] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#212121] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <TrendingUp className="w-3 h-3 mr-2 mt-1 text-[#6b7280] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f3f4f6] text-[#212121] px-2 py-1 rounded border border-[#6b7280]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Key Projects */}
            <div>
              <h2 className="text-xl font-bold text-[#212121] mb-4 border-b-2 border-[#6b7280] pb-2">
                Key Projects
              </h2>
              <div className="space-y-4">
                {analysisProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#212121]">
                    <h3 className="font-bold text-[#212121] text-sm flex items-center">
                      <Target className="w-4 h-4 mr-2 text-[#6b7280]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#6b7280] mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills & Education */}
          <div className="col-span-1">
            
            {/* Core Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#212121] mb-4">
                Core Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#6b7280]/20">
                <div className="space-y-2">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <BarChart3 className="w-3 h-3 mr-2 text-[#6b7280]" />
                      <span className="text-sm text-[#212121]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Technical Tools */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#212121] mb-4">
                Technical Tools
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#6b7280]/20">
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'Excel',
                    'SQL',
                    'Tableau',
                    'Power BI',
                    'Python',
                    'R'
                  ].map((tool, index) => (
                    <div key={index} className="text-center py-2 bg-[#f3f4f6] rounded border border-[#6b7280]/20">
                      <span className="text-sm font-medium text-[#212121]">{tool}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#212121] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-[#6b7280]/20">
                    <h3 className="font-bold text-sm text-[#212121]">
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#6b7280] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#212121]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#212121]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#212121] mb-4">
                Certifications
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#6b7280]/20">
                <div className="space-y-2">
                  {[
                    'Certified Business Analysis Professional',
                    'Six Sigma Green Belt',
                    'Google Analytics Certified',
                    'Microsoft Excel Expert'
                  ].map((cert, index) => (
                    <div key={index} className="flex items-start">
                      <Award className="w-3 h-3 mr-2 mt-0.5 text-[#6b7280] flex-shrink-0" />
                      <span className="text-xs text-[#212121] leading-tight">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#212121] mb-4">
                Professional Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#6b7280]/20">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  {personalInfo.githubUrl && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Github className="w-4 h-4 mr-2" />
                      <span>GitHub</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
