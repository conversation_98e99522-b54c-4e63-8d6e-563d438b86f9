import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Target, Users, Calendar, Award, CheckCircle, TrendingUp } from 'lucide-react';

interface ProjectManagerTemplateProps {
  resumeData: ResumeContent;
}

export default function ProjectManagerTemplate({ resumeData }: ProjectManagerTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock PM projects if none provided
  const pmProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Enterprise Software Migration', description: 'Led cross-functional team of 15+ members, delivered 3 months ahead of schedule' },
    { id: '2', name: 'Digital Transformation Initiative', description: 'Managed $2M budget, achieved 40% efficiency improvement across departments' },
    { id: '3', name: 'Product Launch Campaign', description: 'Coordinated go-to-market strategy, exceeded revenue targets by 25%' }
  ];

  // PM Certifications
  const pmCertifications = [
    'PMP (Project Management Professional)',
    'Certified Scrum Master (CSM)',
    'PRINCE2 Foundation',
    'Agile Certified Practitioner',
    'Six Sigma Green Belt',
    'Microsoft Project Certified'
  ];

  return (
    <div 
      className="bg-[#f9fafb] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="emma-harrison-pm"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Dark Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-[#101214] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'Emma Harrison'}
            </h1>
            <p className="text-lg text-[#505050] flex items-center">
              <Target className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Project Manager'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#505050]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#505050]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#505050]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#505050] pb-2">
              Executive Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#101214]">
              <p className="text-sm text-[#101214] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#505050] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#101214] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#101214] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#505050] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#101214] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <CheckCircle className="w-3 h-3 mr-2 mt-1 text-[#505050] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f9fafb] text-[#101214] px-2 py-1 rounded border border-[#505050]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Key Projects */}
            <div>
              <h2 className="text-xl font-bold text-[#101214] mb-4 border-b-2 border-[#505050] pb-2">
                Key Project Highlights
              </h2>
              <div className="space-y-4">
                {pmProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#101214]">
                    <h3 className="font-bold text-[#101214] text-sm flex items-center">
                      <Target className="w-4 h-4 mr-2 text-[#505050]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#505050] mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills, Education, Certifications */}
          <div className="col-span-1">
            
            {/* PM Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                PM Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                <div className="space-y-2">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <Users className="w-3 h-3 mr-2 text-[#505050]" />
                      <span className="text-sm text-[#101214]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* PM Methodologies */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Methodologies
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                <div className="grid grid-cols-1 gap-2">
                  {[
                    'Agile/Scrum',
                    'Waterfall',
                    'Kanban',
                    'PRINCE2',
                    'Lean Six Sigma',
                    'PMI Framework'
                  ].map((method, index) => (
                    <div key={index} className="text-center py-2 bg-[#f9fafb] rounded border border-[#505050]/20">
                      <span className="text-sm font-medium text-[#101214]">{method}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-[#505050]/20">
                    <h3 className="font-bold text-sm text-[#101214]">
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#505050] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#101214]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#101214]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Certifications
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                <div className="space-y-2">
                  {pmCertifications.slice(0, 6).map((cert, index) => (
                    <div key={index} className="flex items-start">
                      <Award className="w-3 h-3 mr-2 mt-0.5 text-[#505050] flex-shrink-0" />
                      <span className="text-xs text-[#101214] leading-tight">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* PM Tools */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                PM Tools
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'Jira',
                    'Asana',
                    'Trello',
                    'Monday.com',
                    'MS Project',
                    'Slack'
                  ].map((tool, index) => (
                    <div key={index} className="text-center py-1 bg-[#f9fafb] rounded border border-[#505050]/20">
                      <span className="text-xs font-medium text-[#101214]">{tool}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#101214] mb-4">
                Professional Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#505050]/20">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#505050]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#505050]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  <div className="flex items-center text-sm text-[#505050]">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>Schedule Meeting</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
