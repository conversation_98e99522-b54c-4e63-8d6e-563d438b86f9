import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Palette, Eye, Star, Camera } from 'lucide-react';

interface CreativeDesignerSidebarTemplateProps {
  resumeData: ResumeContent;
}

export default function CreativeDesignerSidebarTemplate({ resumeData }: CreativeDesignerSidebarTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-[#e8eaee] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="robyn-kingsley-designer"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Left Sidebar */}
      <div className="absolute top-0 left-0 w-[180px] h-full bg-[#5c6168] px-5 py-8">
        
        {/* Profile Photo */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 rounded-full bg-white border-3 border-[#e8eaee] overflow-hidden mx-auto mb-4 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#30353d] to-[#5c6168] flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
            )}
          </div>
          <h1 className="text-lg font-bold text-white mb-1">
            {personalInfo.fullName || 'Robyn Kingsley'}
          </h1>
          <p className="text-sm text-[#e8eaee] font-medium flex items-center justify-center">
            <Palette className="w-4 h-4 mr-1" />
            {personalInfo.jobTitle || 'Creative Designer'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#e8eaee] mb-4 uppercase tracking-wide">
            Contact
          </h2>
          <div className="space-y-3">
            <div className="flex items-start text-xs text-white">
              <Mail className="w-3 h-3 mr-2 mt-0.5 text-[#e8eaee] flex-shrink-0" />
              <span className="break-all">{personalInfo.email}</span>
            </div>
            <div className="flex items-center text-xs text-white">
              <Phone className="w-3 h-3 mr-2 text-[#e8eaee] flex-shrink-0" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-start text-xs text-white">
              <MapPin className="w-3 h-3 mr-2 mt-0.5 text-[#e8eaee] flex-shrink-0" />
              <span>{personalInfo.location}</span>
            </div>
            {personalInfo.website && (
              <div className="flex items-start text-xs text-white">
                <Globe className="w-3 h-3 mr-2 mt-0.5 text-[#e8eaee] flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-xs text-white">
                <Linkedin className="w-3 h-3 mr-2 text-[#e8eaee] flex-shrink-0" />
                <span>LinkedIn</span>
              </div>
            )}
          </div>
        </div>

        {/* Skills */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#e8eaee] mb-4 uppercase tracking-wide">
            Skills
          </h2>
          <div className="space-y-2">
            {skills.slice(0, 8).map((skill, index) => (
              <div key={index} className="flex items-center">
                <div className="w-2 h-2 bg-[#e8eaee] rounded-full mr-2 flex-shrink-0"></div>
                <span className="text-xs text-white">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Design Tools */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#e8eaee] mb-4 uppercase tracking-wide">
            Tools
          </h2>
          <div className="grid grid-cols-2 gap-2">
            {[
              'Photoshop',
              'Illustrator',
              'InDesign',
              'Figma',
              'Sketch',
              'XD'
            ].map((tool, index) => (
              <div key={index} className="bg-[#30353d] p-1 rounded text-center">
                <span className="text-xs text-white font-medium">{tool}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#e8eaee] mb-4 uppercase tracking-wide">
            Education
          </h2>
          <div className="space-y-3">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-white leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#e8eaee] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#e8eaee]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[180px] w-[414px] h-full px-8 py-8">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#30353d] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#5c6168] mr-3"></div>
              Profile
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#5c6168]">
              <p className="text-sm text-[#30353d] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Professional Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#30353d] mb-6 flex items-center">
            <div className="w-1 h-8 bg-[#5c6168] mr-3"></div>
            Experience
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#30353d] text-base">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-white bg-[#5c6168] px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#5c6168] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#30353d]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#30353d] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <Star className="w-3 h-3 mr-2 mt-0.5 text-[#5c6168] flex-shrink-0" />
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#e8eaee] text-[#30353d] px-2 py-1 rounded border border-[#5c6168]/20">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Creative Specialties */}
        <div>
          <h2 className="text-2xl font-bold text-[#30353d] mb-4 flex items-center">
            <div className="w-1 h-8 bg-[#5c6168] mr-3"></div>
            Creative Specialties
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#30353d] mb-2 flex items-center">
                <Palette className="w-4 h-4 mr-2" />
                Design Areas
              </h3>
              <div className="space-y-1">
                {[
                  'Brand Identity',
                  'Web Design',
                  'Print Design',
                  'UI/UX Design'
                ].map((area, index) => (
                  <div key={index} className="flex items-center">
                    <Eye className="w-3 h-3 mr-2 text-[#5c6168]" />
                    <span className="text-xs text-[#30353d]">{area}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#30353d] mb-2 flex items-center">
                <Camera className="w-4 h-4 mr-2" />
                Services
              </h3>
              <div className="space-y-1">
                {[
                  'Logo Design',
                  'Website Design',
                  'Marketing Materials',
                  'Photography'
                ].map((service, index) => (
                  <div key={index} className="flex items-center">
                    <Star className="w-3 h-3 mr-2 text-[#5c6168]" />
                    <span className="text-xs text-[#30353d]">{service}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
