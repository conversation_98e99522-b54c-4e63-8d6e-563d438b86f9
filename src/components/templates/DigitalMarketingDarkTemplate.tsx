import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, TrendingUp, Target, Users, Megaphone } from 'lucide-react';

interface DigitalMarketingDarkTemplateProps {
  resumeData: ResumeContent;
}

export default function DigitalMarketingDarkTemplate({ resumeData }: DigitalMarketingDarkTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Mock languages data
  const languages = [
    { name: 'English', level: 'Native' },
    { name: 'Hindi', level: 'Native' },
    { name: 'Spanish', level: 'Intermediate' },
    { name: 'French', level: 'Basic' }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="rahul-mehta-marketing"
      style={{ fontFamily: 'IBM Plex Sans, sans-serif' }}
    >
      {/* Dark Sidebar */}
      <div className="absolute top-0 left-0 w-[200px] h-full bg-[#212121] px-6 py-8">
        
        {/* Profile Section */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 rounded-full bg-white border-3 border-[#f3f4f6] overflow-hidden mx-auto mb-4 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#6b7280] to-[#212121] flex items-center justify-center">
                <span className="text-white text-xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'R'}
                </span>
              </div>
            )}
          </div>
          <h1 className="text-lg font-bold text-white mb-1">
            {personalInfo.fullName || 'Rahul Mehta'}
          </h1>
          <p className="text-sm text-[#f3f4f6] font-medium">
            {personalInfo.jobTitle || 'Digital Marketing Specialist'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#f3f4f6] mb-4 uppercase tracking-wide">
            Contact
          </h2>
          <div className="space-y-3">
            <div className="flex items-start text-xs text-white">
              <Mail className="w-3 h-3 mr-2 mt-0.5 text-[#f3f4f6] flex-shrink-0" />
              <span className="break-all">{personalInfo.email}</span>
            </div>
            <div className="flex items-center text-xs text-white">
              <Phone className="w-3 h-3 mr-2 text-[#f3f4f6] flex-shrink-0" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-start text-xs text-white">
              <MapPin className="w-3 h-3 mr-2 mt-0.5 text-[#f3f4f6] flex-shrink-0" />
              <span>{personalInfo.location}</span>
            </div>
            {personalInfo.website && (
              <div className="flex items-start text-xs text-white">
                <Globe className="w-3 h-3 mr-2 mt-0.5 text-[#f3f4f6] flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-xs text-white">
                <Linkedin className="w-3 h-3 mr-2 text-[#f3f4f6] flex-shrink-0" />
                <span>LinkedIn Profile</span>
              </div>
            )}
          </div>
        </div>

        {/* Skills with Progress Bars */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#f3f4f6] mb-4 uppercase tracking-wide">
            Skills
          </h2>
          <div className="space-y-3">
            {skills.slice(0, 6).map((skill, index) => {
              const progress = [95, 90, 85, 88, 82, 87][index] || 80;
              return (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs font-medium text-white">{skill}</span>
                    <span className="text-xs text-[#f3f4f6]">{progress}%</span>
                  </div>
                  <div className="w-full bg-[#404040] rounded-full h-1.5">
                    <div 
                      className="bg-white h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Languages */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#f3f4f6] mb-4 uppercase tracking-wide">
            Languages
          </h2>
          <div className="space-y-2">
            {languages.map((lang, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-xs text-white">{lang.name}</span>
                <span className="text-xs text-[#f3f4f6]">{lang.level}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#f3f4f6] mb-4 uppercase tracking-wide">
            Education
          </h2>
          <div className="space-y-3">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-white leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#f3f4f6] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#f3f4f6]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[200px] w-[394px] h-full px-8 py-8 bg-[#f3f4f6]">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#212121] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#212121] mr-3"></div>
              Profile
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm text-[#212121] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Employment History */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#212121] mb-6 flex items-center">
            <div className="w-1 h-8 bg-[#212121] mr-3"></div>
            Employment History
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#212121] text-base">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-white bg-[#212121] px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#6b7280] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#212121]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#212121] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <TrendingUp className="w-3 h-3 mr-2 mt-0.5 text-[#6b7280] flex-shrink-0" />
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#f3f4f6] text-[#212121] px-2 py-1 rounded border border-[#212121]/20">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Marketing Expertise */}
        <div>
          <h2 className="text-2xl font-bold text-[#212121] mb-4 flex items-center">
            <div className="w-1 h-8 bg-[#212121] mr-3"></div>
            Marketing Expertise
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#212121] mb-2 flex items-center">
                <Megaphone className="w-4 h-4 mr-2" />
                Channels
              </h3>
              <div className="space-y-1">
                {[
                  'Social Media Marketing',
                  'Email Marketing',
                  'Content Marketing',
                  'PPC Advertising'
                ].map((channel, index) => (
                  <div key={index} className="flex items-center">
                    <Target className="w-3 h-3 mr-2 text-[#6b7280]" />
                    <span className="text-xs text-[#212121]">{channel}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#212121] mb-2 flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Platforms
              </h3>
              <div className="space-y-1">
                {[
                  'Google Ads',
                  'Facebook Ads',
                  'LinkedIn Ads',
                  'Instagram Marketing'
                ].map((platform, index) => (
                  <div key={index} className="flex items-center">
                    <TrendingUp className="w-3 h-3 mr-2 text-[#6b7280]" />
                    <span className="text-xs text-[#212121]">{platform}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
