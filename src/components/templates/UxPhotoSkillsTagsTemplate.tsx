import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Palette, Eye, Star } from 'lucide-react';

interface UxPhotoSkillsTagsTemplateProps {
  resumeData: ResumeContent;
}

export default function UxPhotoSkillsTagsTemplate({ resumeData }: UxPhotoSkillsTagsTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-[#e4f2ef] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="james-smith-ux-photo"
      style={{ fontFamily: 'Lora, serif' }}
    >
      {/* Header with Green Accents */}
      <div className="absolute top-0 left-0 w-full h-[140px] bg-white shadow-sm px-8 py-6">
        <div className="flex items-center h-full">
          {/* Large Profile Photo */}
          <div className="w-24 h-24 rounded-full bg-[#379c87] border-4 border-[#e4f2ef] overflow-hidden mr-6 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#379c87] to-[#2d7a6b] flex items-center justify-center">
                <User className="w-10 h-10 text-white" />
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-[#333333] mb-2">
              {personalInfo.fullName || 'James Smith'}
            </h1>
            <p className="text-lg text-[#379c87] flex items-center font-medium">
              <Palette className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UX Designer'}
            </p>
            <div className="flex gap-4 mt-3 text-sm text-[#333333]">
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-1 text-[#379c87]" />
                <span>{personalInfo.email}</span>
              </div>
              <div className="flex items-center">
                <Phone className="w-4 h-4 mr-1 text-[#379c87]" />
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1 text-[#379c87]" />
                <span>{personalInfo.location}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[160px] left-0 w-full h-[682px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#333333] mb-4 flex items-center">
              <div className="w-6 h-6 bg-[#379c87] rounded-full mr-3 flex items-center justify-center">
                <Eye className="w-4 h-4 text-white" />
              </div>
              About Me
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#379c87]">
              <p className="text-sm text-[#333333] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#333333] mb-4 flex items-center">
                <div className="w-6 h-6 bg-[#379c87] rounded-full mr-3 flex items-center justify-center">
                  <Star className="w-4 h-4 text-white" />
                </div>
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 4).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#333333] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#379c87] px-3 py-1 rounded-full">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#379c87] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#333333] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <div className="w-2 h-2 bg-[#379c87] rounded-full mr-2 mt-2 flex-shrink-0"></div>
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#e4f2ef] text-[#379c87] px-2 py-1 rounded-full border border-[#379c87]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <h2 className="text-xl font-bold text-[#333333] mb-4 flex items-center">
                <div className="w-6 h-6 bg-[#379c87] rounded-full mr-3 flex items-center justify-center">
                  <Star className="w-4 h-4 text-white" />
                </div>
                Education
              </h2>
              <div className="space-y-4">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#379c87]">
                    <h3 className="font-bold text-[#333333] text-base">
                      {edu.degree}
                    </h3>
                    <p className="text-sm font-semibold text-[#379c87]">
                      {edu.institution}
                    </p>
                    <p className="text-sm text-[#333333]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-sm text-[#333333]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills Tags */}
          <div className="col-span-1">
            
            {/* Skills Tags */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#333333] mb-4">
                Skills & Expertise
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, index) => {
                    // Vary the tag styles for visual interest
                    const tagStyles = [
                      'bg-[#379c87] text-white',
                      'bg-[#e4f2ef] text-[#379c87] border border-[#379c87]',
                      'bg-[#333333] text-white',
                      'bg-white text-[#379c87] border border-[#379c87]'
                    ];
                    const style = tagStyles[index % tagStyles.length];
                    
                    return (
                      <span key={index} className={`text-xs px-3 py-1 rounded-full font-medium ${style}`}>
                        {skill}
                      </span>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* UX Tools */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#333333] mb-4">
                Design Tools
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="grid grid-cols-2 gap-2">
                  {[
                    'Figma',
                    'Sketch',
                    'Adobe XD',
                    'Principle',
                    'InVision',
                    'Miro'
                  ].map((tool, index) => (
                    <div key={index} className="bg-[#e4f2ef] p-2 rounded text-center border border-[#379c87]/20">
                      <span className="text-sm font-medium text-[#379c87]">{tool}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* UX Methodologies */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#333333] mb-4">
                UX Methods
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-2">
                  {[
                    'User Research',
                    'Wireframing',
                    'Prototyping',
                    'Usability Testing',
                    'Design Systems',
                    'A/B Testing'
                  ].map((method, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 bg-[#379c87] rounded-full mr-2"></div>
                      <span className="text-sm text-[#333333]">{method}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Contact Links */}
            <div>
              <h2 className="text-lg font-bold text-[#333333] mb-4">
                Portfolio Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-3">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#379c87]">
                      <div className="w-6 h-6 bg-[#379c87] rounded-full flex items-center justify-center mr-2">
                        <Globe className="w-3 h-3 text-white" />
                      </div>
                      <span>Portfolio Website</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#379c87]">
                      <div className="w-6 h-6 bg-[#379c87] rounded-full flex items-center justify-center mr-2">
                        <Linkedin className="w-3 h-3 text-white" />
                      </div>
                      <span>LinkedIn</span>
                    </div>
                  )}
                  <div className="flex items-center text-sm text-[#379c87]">
                    <div className="w-6 h-6 bg-[#379c87] rounded-full flex items-center justify-center mr-2">
                      <Palette className="w-3 h-3 text-white" />
                    </div>
                    <span>Dribbble</span>
                  </div>
                  <div className="flex items-center text-sm text-[#379c87]">
                    <div className="w-6 h-6 bg-[#379c87] rounded-full flex items-center justify-center mr-2">
                      <Eye className="w-3 h-3 text-white" />
                    </div>
                    <span>Behance</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
