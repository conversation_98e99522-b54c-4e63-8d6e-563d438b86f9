import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Award, Star, Trophy, Users, BookOpen } from 'lucide-react';

interface ProfessionalSocialAwardsTemplateProps {
  resumeData: ResumeContent;
}

export default function ProfessionalSocialAwardsTemplate({ resumeData }: ProfessionalSocialAwardsTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Mock awards data
  const awards = [
    { name: 'Employee of the Year 2023', organization: 'Current Company' },
    { name: 'Innovation Excellence Award', organization: 'Industry Association' },
    { name: 'Leadership Recognition', organization: 'Professional Society' },
    { name: 'Outstanding Performance Award', organization: 'Previous Company' }
  ];

  // Mock certifications
  const certifications = [
    'Project Management Professional (PMP)',
    'Certified Scrum Master (CSM)',
    'Google Analytics Certified',
    'Microsoft Office Specialist',
    'Six Sigma Green Belt',
    'AWS Cloud Practitioner'
  ];

  // Mock social links
  const socialLinks = [
    { platform: 'LinkedIn', icon: Linkedin, url: personalInfo.linkedinUrl },
    { platform: 'GitHub', icon: Github, url: personalInfo.githubUrl },
    { platform: 'Portfolio', icon: Globe, url: personalInfo.website },
    { platform: 'Twitter', icon: Users, url: '#' }
  ];

  return (
    <div 
      className="bg-[#f3f4f6] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="rukia-sharma-resume"
      style={{ fontFamily: 'Rubik, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-[#414042] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'Rukia Sharma'}
            </h1>
            <p className="text-lg text-[#f3f4f6] font-medium">
              {personalInfo.jobTitle || 'Professional'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#414042] mb-4 border-b-2 border-[#414042] pb-2">
              Professional Profile
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#414042]">
              <p className="text-sm text-[#414042] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#414042] mb-4 border-b-2 border-[#414042] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 4).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#414042] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#414042] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#414042]/70 mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#414042] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <Star className="w-3 h-3 mr-2 mt-1 text-[#414042]/70 flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f3f4f6] text-[#414042] px-2 py-1 rounded border border-[#414042]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <h2 className="text-xl font-bold text-[#414042] mb-4 border-b-2 border-[#414042] pb-2">
                Education
              </h2>
              <div className="space-y-4">
                {education.slice(0, 3).map((edu, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                    <h3 className="font-bold text-[#414042] text-base flex items-center">
                      <BookOpen className="w-4 h-4 mr-2" />
                      {edu.degree}
                    </h3>
                    <p className="text-sm font-semibold text-[#414042]/70">
                      {edu.institution}
                    </p>
                    <p className="text-sm text-[#414042]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-sm text-[#414042]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills, Awards, Certifications, Social */}
          <div className="col-span-1">
            
            {/* Core Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#414042] mb-4">
                Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                <div className="space-y-2">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 bg-[#414042] rounded-full mr-2 flex-shrink-0"></div>
                      <span className="text-sm text-[#414042]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Awards */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#414042] mb-4">
                Awards & Recognition
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                <div className="space-y-3">
                  {awards.slice(0, 4).map((award, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-sm text-[#414042] flex items-center">
                        <Trophy className="w-3 h-3 mr-2" />
                        {award.name}
                      </h3>
                      <p className="text-xs text-[#414042]/70 ml-5">
                        {award.organization}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#414042] mb-4">
                Certifications
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                <div className="space-y-2">
                  {certifications.slice(0, 6).map((cert, index) => (
                    <div key={index} className="flex items-start">
                      <Award className="w-3 h-3 mr-2 mt-0.5 text-[#414042]/70 flex-shrink-0" />
                      <span className="text-xs text-[#414042] leading-tight">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h2 className="text-lg font-bold text-[#414042] mb-4">
                Social Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-[#414042]/20">
                <div className="space-y-3">
                  {socialLinks.map((social, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-6 h-6 bg-[#414042] rounded-full flex items-center justify-center mr-3">
                        <social.icon className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-sm text-[#414042]">{social.platform}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
