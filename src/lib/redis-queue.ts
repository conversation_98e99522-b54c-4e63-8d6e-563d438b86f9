import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';

export interface QueueMessage {
  id: string;
  type: string;
  payload: any;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  scheduledAt?: Date;
  expiresAt?: Date;
  correlationId?: string;
  replyTo?: string;
}

export interface QueueOptions {
  maxRetries?: number;
  retryDelay?: number;
  messageTimeout?: number;
  deadLetterQueue?: string;
}

export class RedisQueue {
  private redis: Redis;
  private logger: Logger;
  private queueName: string;
  private processingQueue: string;
  private deadLetterQueue: string;
  private options: QueueOptions;
  private isProcessing: boolean = false;
  private processingInterval?: NodeJS.Timeout;

  constructor(
    redis: Redis,
    logger: Logger,
    queueName: string,
    options: QueueOptions = {}
  ) {
    this.redis = redis;
    this.logger = logger;
    this.queueName = queueName;
    this.processingQueue = `${queueName}:processing`;
    this.deadLetterQueue = options.deadLetterQueue || `${queueName}:dead`;
    this.options = {
      maxRetries: 3,
      retryDelay: 5000,
      messageTimeout: 300000, // 5 minutes
      ...options,
    };
  }

  async enqueue(
    type: string,
    payload: any,
    options: {
      priority?: 'low' | 'medium' | 'high' | 'urgent';
      delay?: number;
      correlationId?: string;
      replyTo?: string;
      expiresIn?: number;
    } = {}
  ): Promise<string> {
    const messageId = uuidv4();
    const now = new Date();
    
    const message: QueueMessage = {
      id: messageId,
      type,
      payload,
      priority: options.priority || 'medium',
      retryCount: 0,
      maxRetries: this.options.maxRetries!,
      createdAt: now,
      scheduledAt: options.delay ? new Date(now.getTime() + options.delay) : now,
      expiresAt: options.expiresIn ? new Date(now.getTime() + options.expiresIn) : undefined,
      correlationId: options.correlationId,
      replyTo: options.replyTo,
    };

    const queueKey = this.getQueueKey(message.priority);
    const score = message.scheduledAt.getTime();

    await this.redis.zadd(queueKey, score, JSON.stringify(message));
    
    this.logger.debug(`Message enqueued: ${messageId} to ${queueKey}`);
    return messageId;
  }

  async dequeue(timeout: number = 10000): Promise<QueueMessage | null> {
    const now = Date.now();
    const priorityQueues = ['urgent', 'high', 'medium', 'low'];

    for (const priority of priorityQueues) {
      const queueKey = this.getQueueKey(priority as any);
      
      // Get messages that are ready to be processed
      const messages = await this.redis.zrangebyscore(
        queueKey,
        0,
        now,
        'LIMIT',
        0,
        1
      );

      if (messages.length > 0) {
        const messageData = messages[0];
        
        // Atomically move message to processing queue
        const removed = await this.redis.zrem(queueKey, messageData);
        if (removed === 1) {
          const message: QueueMessage = JSON.parse(messageData);
          
          // Check if message has expired
          if (message.expiresAt && new Date() > message.expiresAt) {
            this.logger.warn(`Message expired: ${message.id}`);
            continue;
          }

          // Add to processing queue with timeout
          await this.redis.zadd(
            this.processingQueue,
            now + this.options.messageTimeout!,
            messageData
          );

          this.logger.debug(`Message dequeued: ${message.id}`);
          return message;
        }
      }
    }

    return null;
  }

  async ack(messageId: string): Promise<void> {
    // Remove from processing queue
    const messages = await this.redis.zrange(this.processingQueue, 0, -1);
    
    for (const messageData of messages) {
      const message: QueueMessage = JSON.parse(messageData);
      if (message.id === messageId) {
        await this.redis.zrem(this.processingQueue, messageData);
        this.logger.debug(`Message acknowledged: ${messageId}`);
        return;
      }
    }

    this.logger.warn(`Message not found for ack: ${messageId}`);
  }

  async nack(messageId: string, error?: string): Promise<void> {
    // Find message in processing queue
    const messages = await this.redis.zrange(this.processingQueue, 0, -1);
    
    for (const messageData of messages) {
      const message: QueueMessage = JSON.parse(messageData);
      if (message.id === messageId) {
        // Remove from processing queue
        await this.redis.zrem(this.processingQueue, messageData);
        
        // Increment retry count
        message.retryCount++;
        
        if (message.retryCount <= message.maxRetries) {
          // Requeue with delay
          const delay = this.calculateRetryDelay(message.retryCount);
          message.scheduledAt = new Date(Date.now() + delay);
          
          const queueKey = this.getQueueKey(message.priority);
          await this.redis.zadd(
            queueKey,
            message.scheduledAt.getTime(),
            JSON.stringify(message)
          );
          
          this.logger.debug(`Message requeued: ${messageId} (retry ${message.retryCount})`);
        } else {
          // Move to dead letter queue
          await this.redis.zadd(
            this.deadLetterQueue,
            Date.now(),
            JSON.stringify({ ...message, error })
          );
          
          this.logger.warn(`Message moved to dead letter queue: ${messageId}`);
        }
        return;
      }
    }

    this.logger.warn(`Message not found for nack: ${messageId}`);
  }

  async getQueueStats(): Promise<{
    pending: Record<string, number>;
    processing: number;
    deadLetter: number;
  }> {
    const stats = {
      pending: {} as Record<string, number>,
      processing: 0,
      deadLetter: 0,
    };

    // Count pending messages by priority
    const priorities = ['urgent', 'high', 'medium', 'low'];
    for (const priority of priorities) {
      const queueKey = this.getQueueKey(priority as any);
      stats.pending[priority] = await this.redis.zcard(queueKey);
    }

    // Count processing messages
    stats.processing = await this.redis.zcard(this.processingQueue);

    // Count dead letter messages
    stats.deadLetter = await this.redis.zcard(this.deadLetterQueue);

    return stats;
  }

  async purgeQueue(priority?: 'low' | 'medium' | 'high' | 'urgent'): Promise<number> {
    if (priority) {
      const queueKey = this.getQueueKey(priority);
      return await this.redis.del(queueKey);
    } else {
      const priorities = ['urgent', 'high', 'medium', 'low'];
      let totalRemoved = 0;
      
      for (const p of priorities) {
        const queueKey = this.getQueueKey(p as any);
        totalRemoved += await this.redis.del(queueKey);
      }
      
      return totalRemoved;
    }
  }

  async startProcessing(
    processor: (message: QueueMessage) => Promise<void>,
    concurrency: number = 1
  ): Promise<void> {
    if (this.isProcessing) {
      throw new Error('Queue processing is already started');
    }

    this.isProcessing = true;
    this.logger.info(`Starting queue processing with concurrency: ${concurrency}`);

    // Start processing loop
    this.processingInterval = setInterval(async () => {
      try {
        const promises: Promise<void>[] = [];
        
        for (let i = 0; i < concurrency; i++) {
          promises.push(this.processMessage(processor));
        }
        
        await Promise.all(promises);
      } catch (error) {
        this.logger.error('Error in processing loop:', error);
      }
    }, 1000);

    // Start cleanup of timed-out messages
    setInterval(() => {
      this.cleanupTimedOutMessages();
    }, 30000); // Every 30 seconds
  }

  async stopProcessing(): Promise<void> {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    this.logger.info('Queue processing stopped');
  }

  private async processMessage(
    processor: (message: QueueMessage) => Promise<void>
  ): Promise<void> {
    const message = await this.dequeue();
    if (!message) {
      return;
    }

    try {
      await processor(message);
      await this.ack(message.id);
    } catch (error) {
      this.logger.error(`Message processing failed: ${message.id}`, error);
      await this.nack(message.id, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private async cleanupTimedOutMessages(): Promise<void> {
    const now = Date.now();
    
    // Get timed-out messages
    const timedOutMessages = await this.redis.zrangebyscore(
      this.processingQueue,
      0,
      now
    );

    for (const messageData of timedOutMessages) {
      const message: QueueMessage = JSON.parse(messageData);
      
      // Remove from processing queue
      await this.redis.zrem(this.processingQueue, messageData);
      
      // Requeue or move to dead letter queue
      await this.nack(message.id, 'Message processing timeout');
      
      this.logger.warn(`Message timed out: ${message.id}`);
    }
  }

  private getQueueKey(priority: 'low' | 'medium' | 'high' | 'urgent'): string {
    return `${this.queueName}:${priority}`;
  }

  private calculateRetryDelay(retryCount: number): number {
    // Exponential backoff with jitter
    const baseDelay = this.options.retryDelay!;
    const exponentialDelay = baseDelay * Math.pow(2, retryCount - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    
    return Math.min(exponentialDelay + jitter, 300000); // Max 5 minutes
  }
}

export class MessageRouter {
  private redis: Redis;
  private logger: Logger;
  private subscriptions: Map<string, (message: any) => Promise<void>> = new Map();
  private subscriber: Redis;
  private publisher: Redis;

  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.subscriber = redis.duplicate();
    this.publisher = redis.duplicate();
  }

  async subscribe(
    channel: string,
    handler: (message: any) => Promise<void>
  ): Promise<void> {
    this.subscriptions.set(channel, handler);
    await this.subscriber.subscribe(channel);
    
    this.subscriber.on('message', async (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const parsedMessage = JSON.parse(message);
          await handler(parsedMessage);
        } catch (error) {
          this.logger.error(`Error processing message on channel ${channel}:`, error);
        }
      }
    });

    this.logger.debug(`Subscribed to channel: ${channel}`);
  }

  async unsubscribe(channel: string): Promise<void> {
    this.subscriptions.delete(channel);
    await this.subscriber.unsubscribe(channel);
    this.logger.debug(`Unsubscribed from channel: ${channel}`);
  }

  async publish(channel: string, message: any): Promise<void> {
    await this.publisher.publish(channel, JSON.stringify(message));
    this.logger.debug(`Published message to channel: ${channel}`);
  }

  async request(
    channel: string,
    message: any,
    timeout: number = 30000
  ): Promise<any> {
    const correlationId = uuidv4();
    const replyChannel = `reply:${correlationId}`;
    
    return new Promise(async (resolve, reject) => {
      const timeoutHandle = setTimeout(() => {
        this.unsubscribe(replyChannel);
        reject(new Error('Request timeout'));
      }, timeout);

      await this.subscribe(replyChannel, async (response) => {
        clearTimeout(timeoutHandle);
        await this.unsubscribe(replyChannel);
        resolve(response);
      });

      await this.publish(channel, {
        ...message,
        correlationId,
        replyTo: replyChannel,
      });
    });
  }

  async reply(replyTo: string, message: any): Promise<void> {
    if (replyTo) {
      await this.publish(replyTo, message);
    }
  }

  async disconnect(): Promise<void> {
    await this.subscriber.disconnect();
    await this.publisher.disconnect();
    this.subscriptions.clear();
    this.logger.info('Message router disconnected');
  }
}
