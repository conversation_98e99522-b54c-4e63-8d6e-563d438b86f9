import { Logger } from 'winston';
import { Redis } from 'ioredis';
import { agentConfig } from '../config/agent-config';
import { DecisionResult, DecisionContext } from './ai-decision-engine';
import { v4 as uuidv4 } from 'uuid';

export interface HandoffRequest {
  id: string;
  agentId: string;
  agentType: string;
  userId: string;
  context: DecisionContext;
  decision: DecisionResult;
  reason: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: Date;
  expiresAt: Date;
  status: 'pending' | 'approved' | 'rejected' | 'expired' | 'escalated';
  humanResponse?: {
    decision: 'approve' | 'reject' | 'modify';
    modifiedAction?: string;
    feedback?: string;
    respondedBy: string;
    respondedAt: Date;
  };
  escalationLevel: number;
  metadata: Record<string, any>;
}

export interface HandoffNotification {
  id: string;
  handoffId: string;
  channel: 'email' | 'slack' | 'dashboard' | 'sms';
  recipient: string;
  message: string;
  sentAt: Date;
  status: 'sent' | 'delivered' | 'failed';
}

export class HumanHandoffManager {
  private redis: Redis;
  private logger: Logger;
  private pendingHandoffs: Map<string, HandoffRequest> = new Map();
  private notificationHandlers: Map<string, (notification: HandoffNotification) => Promise<void>> = new Map();

  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.initializeNotificationHandlers();
    this.startExpirationMonitoring();
  }

  async requestHumanApproval(
    agentId: string,
    agentType: string,
    userId: string,
    context: DecisionContext,
    decision: DecisionResult,
    reason: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'
  ): Promise<HandoffRequest> {
    const handoffId = uuidv4();
    const config = agentConfig.humanHandoff;
    
    const handoffRequest: HandoffRequest = {
      id: handoffId,
      agentId,
      agentType,
      userId,
      context,
      decision,
      reason,
      priority,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + config.escalation.responseTimeout),
      status: 'pending',
      escalationLevel: 0,
      metadata: {
        originalConfidence: decision.confidence,
        triggeredBy: this.identifyTrigger(decision, context),
        estimatedImpact: this.assessImpact(context, decision),
      }
    };

    // Store in Redis and local cache
    await this.redis.setex(
      `handoff:${handoffId}`,
      Math.ceil(config.escalation.responseTimeout / 1000),
      JSON.stringify(handoffRequest)
    );
    this.pendingHandoffs.set(handoffId, handoffRequest);

    // Send notifications
    await this.sendNotifications(handoffRequest);

    // Log the handoff request
    this.logger.info(`Human handoff requested: ${handoffId}`, {
      agentType,
      reason,
      priority,
      confidence: decision.confidence
    });

    return handoffRequest;
  }

  async respondToHandoff(
    handoffId: string,
    decision: 'approve' | 'reject' | 'modify',
    respondedBy: string,
    modifiedAction?: string,
    feedback?: string
  ): Promise<HandoffRequest | null> {
    const handoff = await this.getHandoffRequest(handoffId);
    if (!handoff || handoff.status !== 'pending') {
      this.logger.warn(`Invalid handoff response attempt: ${handoffId}`);
      return null;
    }

    handoff.status = decision === 'approve' ? 'approved' : 'rejected';
    handoff.humanResponse = {
      decision,
      modifiedAction,
      feedback,
      respondedBy,
      respondedAt: new Date()
    };

    // Update in Redis and local cache
    await this.redis.setex(
      `handoff:${handoffId}`,
      86400, // Keep for 24 hours after response
      JSON.stringify(handoff)
    );
    this.pendingHandoffs.set(handoffId, handoff);

    // Notify the waiting agent
    await this.notifyAgentOfResponse(handoff);

    this.logger.info(`Human handoff responded: ${handoffId}`, {
      decision,
      respondedBy,
      feedback: feedback ? 'provided' : 'none'
    });

    return handoff;
  }

  async getHandoffRequest(handoffId: string): Promise<HandoffRequest | null> {
    // Check local cache first
    let handoff = this.pendingHandoffs.get(handoffId);
    
    if (!handoff) {
      // Check Redis
      const redisData = await this.redis.get(`handoff:${handoffId}`);
      if (redisData) {
        handoff = JSON.parse(redisData);
        this.pendingHandoffs.set(handoffId, handoff!);
      }
    }

    return handoff || null;
  }

  async getPendingHandoffs(userId?: string, agentType?: string): Promise<HandoffRequest[]> {
    const allHandoffs = Array.from(this.pendingHandoffs.values())
      .filter(h => h.status === 'pending');

    let filtered = allHandoffs;
    
    if (userId) {
      filtered = filtered.filter(h => h.userId === userId);
    }
    
    if (agentType) {
      filtered = filtered.filter(h => h.agentType === agentType);
    }

    return filtered.sort((a, b) => {
      // Sort by priority and creation time
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
  }

  async getHandoffStats(): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    expired: number;
    averageResponseTime: number;
    byPriority: Record<string, number>;
    byAgentType: Record<string, number>;
  }> {
    const allHandoffs = Array.from(this.pendingHandoffs.values());
    
    const stats = {
      total: allHandoffs.length,
      pending: allHandoffs.filter(h => h.status === 'pending').length,
      approved: allHandoffs.filter(h => h.status === 'approved').length,
      rejected: allHandoffs.filter(h => h.status === 'rejected').length,
      expired: allHandoffs.filter(h => h.status === 'expired').length,
      averageResponseTime: 0,
      byPriority: {} as Record<string, number>,
      byAgentType: {} as Record<string, number>
    };

    // Calculate average response time
    const respondedHandoffs = allHandoffs.filter(h => h.humanResponse);
    if (respondedHandoffs.length > 0) {
      const totalResponseTime = respondedHandoffs.reduce((sum, h) => {
        const responseTime = h.humanResponse!.respondedAt.getTime() - h.createdAt.getTime();
        return sum + responseTime;
      }, 0);
      stats.averageResponseTime = totalResponseTime / respondedHandoffs.length;
    }

    // Group by priority
    allHandoffs.forEach(h => {
      stats.byPriority[h.priority] = (stats.byPriority[h.priority] || 0) + 1;
      stats.byAgentType[h.agentType] = (stats.byAgentType[h.agentType] || 0) + 1;
    });

    return stats;
  }

  private async sendNotifications(handoff: HandoffRequest): Promise<void> {
    const config = agentConfig.humanHandoff;
    
    for (const channel of config.escalation.channels) {
      try {
        const notification: HandoffNotification = {
          id: uuidv4(),
          handoffId: handoff.id,
          channel,
          recipient: await this.getRecipientForChannel(channel, handoff.userId),
          message: this.buildNotificationMessage(handoff),
          sentAt: new Date(),
          status: 'sent'
        };

        const handler = this.notificationHandlers.get(channel);
        if (handler) {
          await handler(notification);
          this.logger.debug(`Notification sent via ${channel} for handoff ${handoff.id}`);
        } else {
          this.logger.warn(`No handler for notification channel: ${channel}`);
        }
      } catch (error) {
        this.logger.error(`Failed to send notification via ${channel}:`, error);
      }
    }
  }

  private buildNotificationMessage(handoff: HandoffRequest): string {
    const urgencyEmoji = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      urgent: '🔴'
    };

    return `
${urgencyEmoji[handoff.priority]} **Human Approval Required**

**Agent:** ${handoff.agentType}
**Situation:** ${handoff.context.situation}
**Proposed Action:** ${handoff.decision.action}
**Confidence:** ${(handoff.decision.confidence * 100).toFixed(1)}%
**Reason:** ${handoff.reason}

**Reasoning:** ${handoff.decision.reasoning}

**Available Actions:**
${handoff.context.availableActions.map(action => `• ${action}`).join('\n')}

**Expires:** ${handoff.expiresAt.toLocaleString()}

[View Details](${process.env.DASHBOARD_URL}/handoffs/${handoff.id})
    `.trim();
  }

  private async getRecipientForChannel(channel: string, userId: string): Promise<string> {
    switch (channel) {
      case 'email':
        // Get user email from database
        return `user-${userId}@example.com`; // Placeholder
      case 'slack':
        return `@user-${userId}`; // Placeholder
      case 'dashboard':
        return userId;
      case 'sms':
        return `+1234567890`; // Placeholder
      default:
        return userId;
    }
  }

  private initializeNotificationHandlers(): void {
    // Dashboard notification (real-time updates)
    this.notificationHandlers.set('dashboard', async (notification) => {
      await this.redis.publish(
        `dashboard:${notification.recipient}`,
        JSON.stringify({
          type: 'handoff_request',
          data: notification
        })
      );
    });

    // Email notification (placeholder)
    this.notificationHandlers.set('email', async (notification) => {
      // Integrate with email service (SendGrid, SES, etc.)
      this.logger.info(`Email notification sent to ${notification.recipient}`);
    });

    // Slack notification (placeholder)
    this.notificationHandlers.set('slack', async (notification) => {
      // Integrate with Slack API
      this.logger.info(`Slack notification sent to ${notification.recipient}`);
    });

    // SMS notification (placeholder)
    this.notificationHandlers.set('sms', async (notification) => {
      // Integrate with SMS service (Twilio, etc.)
      this.logger.info(`SMS notification sent to ${notification.recipient}`);
    });
  }

  private async notifyAgentOfResponse(handoff: HandoffRequest): Promise<void> {
    await this.redis.publish(
      `agent:${handoff.agentId}:handoff_response`,
      JSON.stringify({
        handoffId: handoff.id,
        decision: handoff.humanResponse!.decision,
        modifiedAction: handoff.humanResponse!.modifiedAction,
        feedback: handoff.humanResponse!.feedback
      })
    );
  }

  private identifyTrigger(decision: DecisionResult, context: DecisionContext): string {
    const config = agentConfig.humanHandoff.triggers;
    
    if (decision.confidence < config.lowConfidence) {
      return 'low_confidence';
    }
    
    if (config.complexDecision && context.availableActions.length > 5) {
      return 'complex_decision';
    }
    
    return 'unknown';
  }

  private assessImpact(context: DecisionContext, decision: DecisionResult): 'low' | 'medium' | 'high' {
    // Simple impact assessment based on context
    if (context.urgency === 'high' || context.riskTolerance === 'conservative') {
      return 'high';
    }
    
    if (decision.confidence < 0.5) {
      return 'high';
    }
    
    if (context.urgency === 'medium') {
      return 'medium';
    }
    
    return 'low';
  }

  private startExpirationMonitoring(): void {
    setInterval(async () => {
      const now = new Date();
      const expiredHandoffs = Array.from(this.pendingHandoffs.values())
        .filter(h => h.status === 'pending' && h.expiresAt < now);

      for (const handoff of expiredHandoffs) {
        handoff.status = 'expired';
        
        // Update in Redis
        await this.redis.setex(
          `handoff:${handoff.id}`,
          86400, // Keep for 24 hours
          JSON.stringify(handoff)
        );

        // Notify agent of expiration
        await this.redis.publish(
          `agent:${handoff.agentId}:handoff_expired`,
          JSON.stringify({ handoffId: handoff.id })
        );

        this.logger.warn(`Handoff expired: ${handoff.id}`);
      }
    }, 60000); // Check every minute
  }
}
