import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { Logger } from 'winston';
import { agentConfig } from '../config/agent-config';

export interface DecisionContext {
  situation: string;
  availableActions: string[];
  constraints: Record<string, any>;
  historicalData?: any[];
  confidence?: number;
  urgency?: 'low' | 'medium' | 'high';
  riskTolerance?: 'conservative' | 'moderate' | 'aggressive';
}

export interface DecisionResult {
  action: string;
  confidence: number;
  reasoning: string;
  alternatives: Array<{
    action: string;
    confidence: number;
    reasoning: string;
  }>;
  requiresHumanApproval: boolean;
  metadata: Record<string, any>;
}

export interface LearningData {
  context: DecisionContext;
  decision: DecisionResult;
  outcome: 'success' | 'failure' | 'partial';
  feedback?: string;
  timestamp: Date;
}

export class AIDecisionEngine {
  private openai: OpenAI;
  private anthropic: Anthropic;
  private logger: Logger;
  private learningHistory: LearningData[] = [];
  private decisionPatterns: Map<string, any> = new Map();

  constructor(logger: Logger) {
    this.logger = logger;
    this.openai = new OpenAI({
      apiKey: agentConfig.ai.openai.apiKey,
    });
    this.anthropic = new Anthropic({
      apiKey: agentConfig.ai.anthropic.apiKey,
    });
  }

  async makeDecision(context: DecisionContext): Promise<DecisionResult> {
    try {
      // Check if this is a learned pattern
      const learnedDecision = this.checkLearnedPatterns(context);
      if (learnedDecision && learnedDecision.confidence > 0.9) {
        this.logger.info('Using learned pattern for decision');
        return learnedDecision;
      }

      // Use AI for complex decision making
      const aiDecision = await this.getAIDecision(context);
      
      // Apply safety checks and constraints
      const validatedDecision = this.validateDecision(aiDecision, context);
      
      // Determine if human approval is needed
      validatedDecision.requiresHumanApproval = this.requiresHumanApproval(validatedDecision, context);
      
      // Store for learning
      this.storeDecisionContext(context, validatedDecision);
      
      return validatedDecision;

    } catch (error) {
      this.logger.error('Decision making failed:', error);
      return this.getFallbackDecision(context);
    }
  }

  private checkLearnedPatterns(context: DecisionContext): DecisionResult | null {
    const situationHash = this.hashSituation(context.situation);
    const pattern = this.decisionPatterns.get(situationHash);
    
    if (pattern && pattern.successRate > 0.8) {
      return {
        action: pattern.action,
        confidence: pattern.confidence,
        reasoning: `Learned pattern: ${pattern.reasoning}`,
        alternatives: pattern.alternatives || [],
        requiresHumanApproval: false,
        metadata: { source: 'learned_pattern', pattern: situationHash }
      };
    }
    
    return null;
  }

  private async getAIDecision(context: DecisionContext): Promise<DecisionResult> {
    const prompt = this.buildDecisionPrompt(context);
    
    try {
      // Try OpenAI first
      const response = await this.openai.chat.completions.create({
        model: agentConfig.ai.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert AI decision-making system for job application automation. Analyze the situation and provide the best action with detailed reasoning.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: agentConfig.ai.openai.temperature,
        max_tokens: agentConfig.ai.openai.maxTokens,
      });

      const aiResponse = response.choices[0]?.message?.content;
      if (aiResponse) {
        return this.parseAIResponse(aiResponse, context);
      }
    } catch (error) {
      this.logger.warn('OpenAI decision failed, trying Anthropic:', error);
    }

    // Fallback to Anthropic
    try {
      const response = await this.anthropic.messages.create({
        model: agentConfig.ai.anthropic.model,
        max_tokens: agentConfig.ai.anthropic.maxTokens,
        temperature: agentConfig.ai.anthropic.temperature,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
      });

      const aiResponse = response.content[0]?.type === 'text' ? response.content[0].text : '';
      if (aiResponse) {
        return this.parseAIResponse(aiResponse, context);
      }
    } catch (error) {
      this.logger.error('Anthropic decision failed:', error);
    }

    throw new Error('All AI providers failed');
  }

  private buildDecisionPrompt(context: DecisionContext): string {
    const historicalContext = this.getRelevantHistory(context);
    
    return `
DECISION CONTEXT:
Situation: ${context.situation}
Available Actions: ${context.availableActions.join(', ')}
Constraints: ${JSON.stringify(context.constraints, null, 2)}
Urgency: ${context.urgency || 'medium'}
Risk Tolerance: ${context.riskTolerance || 'moderate'}

HISTORICAL CONTEXT:
${historicalContext}

REQUIREMENTS:
1. Choose the best action from the available options
2. Provide confidence score (0-1)
3. Explain your reasoning
4. Suggest 2-3 alternative actions with their confidence scores
5. Consider the constraints and historical outcomes

RESPONSE FORMAT (JSON):
{
  "action": "chosen_action",
  "confidence": 0.85,
  "reasoning": "detailed explanation",
  "alternatives": [
    {"action": "alternative1", "confidence": 0.7, "reasoning": "why this could work"},
    {"action": "alternative2", "confidence": 0.6, "reasoning": "backup option"}
  ]
}
`;
  }

  private getRelevantHistory(context: DecisionContext): string {
    const relevantHistory = this.learningHistory
      .filter(entry => this.isSimilarContext(entry.context, context))
      .slice(-5) // Last 5 relevant decisions
      .map(entry => `
        Situation: ${entry.context.situation}
        Decision: ${entry.decision.action}
        Outcome: ${entry.outcome}
        ${entry.feedback ? `Feedback: ${entry.feedback}` : ''}
      `);

    return relevantHistory.length > 0 
      ? `Previous similar decisions:\n${relevantHistory.join('\n')}`
      : 'No relevant historical data available.';
  }

  private isSimilarContext(context1: DecisionContext, context2: DecisionContext): boolean {
    // Simple similarity check - could be enhanced with ML
    const similarity = this.calculateSimilarity(context1.situation, context2.situation);
    return similarity > 0.7;
  }

  private calculateSimilarity(text1: string, text2: string): number {
    // Simple Jaccard similarity
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  private parseAIResponse(response: string, context: DecisionContext): DecisionResult {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          action: parsed.action,
          confidence: Math.min(Math.max(parsed.confidence, 0), 1),
          reasoning: parsed.reasoning,
          alternatives: parsed.alternatives || [],
          requiresHumanApproval: false,
          metadata: { source: 'ai_decision', model: 'gpt-4' }
        };
      }
    } catch (error) {
      this.logger.warn('Failed to parse AI JSON response, using fallback parsing');
    }

    // Fallback parsing
    return this.parseTextResponse(response, context);
  }

  private parseTextResponse(response: string, context: DecisionContext): DecisionResult {
    // Extract action (first available action mentioned)
    const action = context.availableActions.find(a => 
      response.toLowerCase().includes(a.toLowerCase())
    ) || context.availableActions[0];

    // Extract confidence (look for numbers between 0 and 1)
    const confidenceMatch = response.match(/confidence[:\s]*([0-9.]+)/i);
    const confidence = confidenceMatch ? parseFloat(confidenceMatch[1]) : 0.7;

    return {
      action,
      confidence: Math.min(Math.max(confidence, 0), 1),
      reasoning: response.substring(0, 500), // First 500 chars as reasoning
      alternatives: [],
      requiresHumanApproval: false,
      metadata: { source: 'ai_decision_fallback' }
    };
  }

  private validateDecision(decision: DecisionResult, context: DecisionContext): DecisionResult {
    // Ensure action is valid
    if (!context.availableActions.includes(decision.action)) {
      this.logger.warn(`Invalid action ${decision.action}, using first available action`);
      decision.action = context.availableActions[0];
      decision.confidence *= 0.5; // Reduce confidence for fallback
    }

    // Apply confidence threshold
    if (decision.confidence < agentConfig.ai.decisionMaking.confidenceThreshold) {
      decision.requiresHumanApproval = true;
    }

    // Check constraints
    for (const [constraint, value] of Object.entries(context.constraints)) {
      if (!this.satisfiesConstraint(decision.action, constraint, value)) {
        decision.confidence *= 0.8; // Reduce confidence for constraint violations
        decision.requiresHumanApproval = true;
      }
    }

    return decision;
  }

  private satisfiesConstraint(action: string, constraint: string, value: any): boolean {
    // Implement constraint checking logic
    switch (constraint) {
      case 'maxRetries':
        return true; // Assume action respects retry limits
      case 'timeLimit':
        return true; // Assume action can be completed in time
      case 'riskLevel':
        return value !== 'low' || !action.includes('aggressive');
      default:
        return true;
    }
  }

  private requiresHumanApproval(decision: DecisionResult, context: DecisionContext): boolean {
    const config = agentConfig.humanHandoff;
    
    // Low confidence
    if (decision.confidence < config.triggers.lowConfidence) {
      return true;
    }

    // Complex decision
    if (config.triggers.complexDecision && context.availableActions.length > 5) {
      return true;
    }

    // High risk situations
    if (context.riskTolerance === 'conservative' && decision.confidence < 0.9) {
      return true;
    }

    return false;
  }

  private getFallbackDecision(context: DecisionContext): DecisionResult {
    return {
      action: context.availableActions[0], // Default to first action
      confidence: 0.3,
      reasoning: 'Fallback decision due to AI system failure',
      alternatives: [],
      requiresHumanApproval: true,
      metadata: { source: 'fallback' }
    };
  }

  private storeDecisionContext(context: DecisionContext, decision: DecisionResult): void {
    // Store for future learning (simplified)
    const situationHash = this.hashSituation(context.situation);
    this.decisionPatterns.set(situationHash, {
      action: decision.action,
      confidence: decision.confidence,
      reasoning: decision.reasoning,
      alternatives: decision.alternatives,
      count: (this.decisionPatterns.get(situationHash)?.count || 0) + 1,
      successRate: 0.5 // Will be updated with feedback
    });
  }

  private hashSituation(situation: string): string {
    // Simple hash function for situation categorization
    return situation.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .split(/\s+/)
      .sort()
      .slice(0, 5) // Top 5 keywords
      .join('_');
  }

  public async learnFromOutcome(
    context: DecisionContext,
    decision: DecisionResult,
    outcome: 'success' | 'failure' | 'partial',
    feedback?: string
  ): Promise<void> {
    const learningData: LearningData = {
      context,
      decision,
      outcome,
      feedback,
      timestamp: new Date()
    };

    this.learningHistory.push(learningData);

    // Update decision patterns
    const situationHash = this.hashSituation(context.situation);
    const pattern = this.decisionPatterns.get(situationHash);
    
    if (pattern) {
      const successValue = outcome === 'success' ? 1 : outcome === 'partial' ? 0.5 : 0;
      pattern.successRate = (pattern.successRate * (pattern.count - 1) + successValue) / pattern.count;
      
      // Adjust confidence based on outcomes
      if (outcome === 'success') {
        pattern.confidence = Math.min(pattern.confidence * 1.1, 1.0);
      } else if (outcome === 'failure') {
        pattern.confidence = Math.max(pattern.confidence * 0.9, 0.1);
      }
    }

    // Limit history size
    if (this.learningHistory.length > 1000) {
      this.learningHistory = this.learningHistory.slice(-500);
    }

    this.logger.info(`Learned from outcome: ${outcome} for situation: ${context.situation}`);
  }

  public getDecisionStats(): any {
    return {
      totalDecisions: this.learningHistory.length,
      successRate: this.learningHistory.filter(d => d.outcome === 'success').length / this.learningHistory.length,
      learnedPatterns: this.decisionPatterns.size,
      averageConfidence: Array.from(this.decisionPatterns.values())
        .reduce((sum, p) => sum + p.confidence, 0) / this.decisionPatterns.size
    };
  }
}
