import { Logger } from 'winston';
import { Redis } from 'ioredis';
import { agentConfig } from '../config/agent-config';

export interface PerformanceMetrics {
  timestamp: Date;
  agentType: string;
  taskType: string;
  duration: number;
  success: boolean;
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  queueWaitTime: number;
  errorRate: number;
}

export interface OptimizationRecommendation {
  type: 'scaling' | 'caching' | 'batching' | 'resource_allocation';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  expectedImprovement: number; // percentage
  implementationCost: 'low' | 'medium' | 'high';
  action: string;
  parameters: Record<string, any>;
}

export class PerformanceOptimizer {
  private redis: Redis;
  private logger: Logger;
  private metrics: PerformanceMetrics[] = [];
  private optimizationHistory: Map<string, OptimizationRecommendation[]> = new Map();
  private performanceBaselines: Map<string, number> = new Map();

  constructor(redis: Redis, logger: Logger) {
    this.redis = redis;
    this.logger = logger;
    this.initializeBaselines();
    this.startPerformanceMonitoring();
  }

  async recordMetric(metric: PerformanceMetrics): Promise<void> {
    this.metrics.push(metric);
    
    // Store in Redis for persistence
    await this.redis.zadd(
      `metrics:${metric.agentType}:${metric.taskType}`,
      metric.timestamp.getTime(),
      JSON.stringify(metric)
    );

    // Limit in-memory metrics
    if (this.metrics.length > 10000) {
      this.metrics = this.metrics.slice(-5000);
    }

    // Trigger optimization analysis if needed
    if (this.shouldAnalyzePerformance(metric)) {
      await this.analyzeAndOptimize(metric.agentType, metric.taskType);
    }
  }

  async analyzeAndOptimize(agentType: string, taskType: string): Promise<OptimizationRecommendation[]> {
    const recentMetrics = await this.getRecentMetrics(agentType, taskType, 100);
    const recommendations: OptimizationRecommendation[] = [];

    // Analyze different optimization opportunities
    recommendations.push(...await this.analyzeScalingNeeds(recentMetrics));
    recommendations.push(...await this.analyzeCachingOpportunities(recentMetrics));
    recommendations.push(...await this.analyzeBatchingPotential(recentMetrics));
    recommendations.push(...await this.analyzeResourceAllocation(recentMetrics));

    // Sort by priority and expected improvement
    recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.expectedImprovement - a.expectedImprovement;
    });

    // Store recommendations
    this.optimizationHistory.set(`${agentType}:${taskType}`, recommendations);

    // Auto-implement low-risk, high-impact optimizations
    await this.autoImplementOptimizations(recommendations);

    this.logger.info(`Generated ${recommendations.length} optimization recommendations for ${agentType}:${taskType}`);
    return recommendations;
  }

  private async analyzeScalingNeeds(metrics: PerformanceMetrics[]): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    
    if (metrics.length < 10) return recommendations;

    const avgQueueWaitTime = metrics.reduce((sum, m) => sum + m.queueWaitTime, 0) / metrics.length;
    const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
    const errorRate = metrics.filter(m => !m.success).length / metrics.length;

    // High queue wait time suggests need for horizontal scaling
    if (avgQueueWaitTime > 30000) { // 30 seconds
      recommendations.push({
        type: 'scaling',
        priority: avgQueueWaitTime > 120000 ? 'critical' : 'high',
        description: `High queue wait time (${(avgQueueWaitTime / 1000).toFixed(1)}s) indicates need for more agent instances`,
        expectedImprovement: Math.min(50, avgQueueWaitTime / 1000),
        implementationCost: 'medium',
        action: 'scale_horizontal',
        parameters: {
          currentInstances: await this.getCurrentInstanceCount(metrics[0].agentType),
          recommendedInstances: Math.ceil(avgQueueWaitTime / 15000), // Target 15s max wait
          scaleUpBy: Math.max(1, Math.floor(avgQueueWaitTime / 30000))
        }
      });
    }

    // High error rate might indicate resource constraints
    if (errorRate > 0.1) { // 10% error rate
      recommendations.push({
        type: 'scaling',
        priority: errorRate > 0.2 ? 'critical' : 'high',
        description: `High error rate (${(errorRate * 100).toFixed(1)}%) may indicate resource constraints`,
        expectedImprovement: (errorRate - 0.05) * 100,
        implementationCost: 'medium',
        action: 'increase_resources',
        parameters: {
          currentCpuLimit: '1000m',
          recommendedCpuLimit: '2000m',
          currentMemoryLimit: '2Gi',
          recommendedMemoryLimit: '4Gi'
        }
      });
    }

    return recommendations;
  }

  private async analyzeCachingOpportunities(metrics: PerformanceMetrics[]): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    
    // Analyze task patterns for caching opportunities
    const taskPatterns = new Map<string, number>();
    metrics.forEach(metric => {
      const pattern = this.extractTaskPattern(metric);
      taskPatterns.set(pattern, (taskPatterns.get(pattern) || 0) + 1);
    });

    // Find frequently repeated patterns
    for (const [pattern, count] of taskPatterns.entries()) {
      if (count > 5 && count / metrics.length > 0.1) { // 10% of tasks
        const avgDuration = metrics
          .filter(m => this.extractTaskPattern(m) === pattern)
          .reduce((sum, m) => sum + m.duration, 0) / count;

        if (avgDuration > 10000) { // 10 seconds
          recommendations.push({
            type: 'caching',
            priority: avgDuration > 60000 ? 'high' : 'medium',
            description: `Frequent pattern "${pattern}" takes ${(avgDuration / 1000).toFixed(1)}s - good caching candidate`,
            expectedImprovement: Math.min(80, avgDuration / 1000),
            implementationCost: 'low',
            action: 'implement_caching',
            parameters: {
              pattern,
              frequency: count,
              avgDuration,
              cacheKey: this.generateCacheKey(pattern),
              ttl: Math.min(3600, avgDuration / 100) // TTL based on computation time
            }
          });
        }
      }
    }

    return recommendations;
  }

  private async analyzeBatchingPotential(metrics: PerformanceMetrics[]): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    
    // Group metrics by time windows to find batching opportunities
    const timeWindows = new Map<number, PerformanceMetrics[]>();
    const windowSize = 60000; // 1 minute windows

    metrics.forEach(metric => {
      const window = Math.floor(metric.timestamp.getTime() / windowSize);
      if (!timeWindows.has(window)) {
        timeWindows.set(window, []);
      }
      timeWindows.get(window)!.push(metric);
    });

    // Find windows with high task density
    for (const [window, windowMetrics] of timeWindows.entries()) {
      if (windowMetrics.length > 10) { // High density
        const similarTasks = this.groupSimilarTasks(windowMetrics);
        
        for (const [taskGroup, tasks] of similarTasks.entries()) {
          if (tasks.length > 3) {
            const totalDuration = tasks.reduce((sum, t) => sum + t.duration, 0);
            const avgDuration = totalDuration / tasks.length;
            
            recommendations.push({
              type: 'batching',
              priority: tasks.length > 10 ? 'high' : 'medium',
              description: `${tasks.length} similar tasks in 1-minute window - batching could reduce overhead`,
              expectedImprovement: Math.min(40, tasks.length * 2),
              implementationCost: 'medium',
              action: 'implement_batching',
              parameters: {
                taskGroup,
                batchSize: Math.min(10, tasks.length),
                estimatedSavings: totalDuration * 0.3, // 30% savings estimate
                windowSize: windowSize
              }
            });
          }
        }
      }
    }

    return recommendations;
  }

  private async analyzeResourceAllocation(metrics: PerformanceMetrics[]): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    
    // Analyze resource usage patterns
    const avgCpu = metrics.reduce((sum, m) => sum + m.resourceUsage.cpu, 0) / metrics.length;
    const avgMemory = metrics.reduce((sum, m) => sum + m.resourceUsage.memory, 0) / metrics.length;
    const maxCpu = Math.max(...metrics.map(m => m.resourceUsage.cpu));
    const maxMemory = Math.max(...metrics.map(m => m.resourceUsage.memory));

    // CPU optimization
    if (avgCpu < 30 && maxCpu < 60) { // Underutilized CPU
      recommendations.push({
        type: 'resource_allocation',
        priority: 'medium',
        description: `Low CPU utilization (avg: ${avgCpu.toFixed(1)}%) - can reduce CPU allocation`,
        expectedImprovement: 20,
        implementationCost: 'low',
        action: 'reduce_cpu_allocation',
        parameters: {
          currentCpu: '1000m',
          recommendedCpu: '500m',
          estimatedSavings: '50%'
        }
      });
    } else if (avgCpu > 80 || maxCpu > 95) { // CPU constrained
      recommendations.push({
        type: 'resource_allocation',
        priority: maxCpu > 95 ? 'high' : 'medium',
        description: `High CPU utilization (avg: ${avgCpu.toFixed(1)}%, max: ${maxCpu.toFixed(1)}%) - increase CPU allocation`,
        expectedImprovement: 30,
        implementationCost: 'medium',
        action: 'increase_cpu_allocation',
        parameters: {
          currentCpu: '1000m',
          recommendedCpu: '2000m'
        }
      });
    }

    // Memory optimization
    if (avgMemory < 512 && maxMemory < 1024) { // Underutilized memory
      recommendations.push({
        type: 'resource_allocation',
        priority: 'low',
        description: `Low memory utilization (avg: ${avgMemory.toFixed(0)}MB) - can reduce memory allocation`,
        expectedImprovement: 15,
        implementationCost: 'low',
        action: 'reduce_memory_allocation',
        parameters: {
          currentMemory: '2Gi',
          recommendedMemory: '1Gi'
        }
      });
    }

    return recommendations;
  }

  private async autoImplementOptimizations(recommendations: OptimizationRecommendation[]): Promise<void> {
    const autoImplementable = recommendations.filter(r => 
      r.implementationCost === 'low' && 
      r.expectedImprovement > 20 &&
      ['caching', 'resource_allocation'].includes(r.type)
    );

    for (const recommendation of autoImplementable) {
      try {
        await this.implementOptimization(recommendation);
        this.logger.info(`Auto-implemented optimization: ${recommendation.description}`);
      } catch (error) {
        this.logger.error(`Failed to auto-implement optimization: ${recommendation.description}`, error);
      }
    }
  }

  private async implementOptimization(recommendation: OptimizationRecommendation): Promise<void> {
    switch (recommendation.action) {
      case 'implement_caching':
        await this.enableCaching(recommendation.parameters);
        break;
      case 'reduce_cpu_allocation':
      case 'increase_cpu_allocation':
      case 'reduce_memory_allocation':
        await this.updateResourceAllocation(recommendation.parameters);
        break;
      case 'scale_horizontal':
        await this.scaleHorizontally(recommendation.parameters);
        break;
      default:
        this.logger.warn(`Unknown optimization action: ${recommendation.action}`);
    }
  }

  private async enableCaching(parameters: any): Promise<void> {
    const cacheConfig = {
      key: parameters.cacheKey,
      ttl: parameters.ttl,
      pattern: parameters.pattern
    };
    
    await this.redis.setex(
      `cache_config:${parameters.pattern}`,
      86400, // 24 hours
      JSON.stringify(cacheConfig)
    );
  }

  private async updateResourceAllocation(parameters: any): Promise<void> {
    // In a real implementation, this would update Kubernetes resource limits
    this.logger.info(`Resource allocation update: ${JSON.stringify(parameters)}`);
  }

  private async scaleHorizontally(parameters: any): Promise<void> {
    // In a real implementation, this would update Kubernetes HPA or deployment replicas
    this.logger.info(`Horizontal scaling: ${JSON.stringify(parameters)}`);
  }

  private shouldAnalyzePerformance(metric: PerformanceMetrics): boolean {
    const key = `${metric.agentType}:${metric.taskType}`;
    const lastAnalysis = this.optimizationHistory.get(key);
    
    if (!lastAnalysis) return true;
    
    // Analyze every 100 metrics or if performance degrades significantly
    const recentMetrics = this.metrics.filter(m => 
      m.agentType === metric.agentType && 
      m.taskType === metric.taskType &&
      m.timestamp.getTime() > Date.now() - 3600000 // Last hour
    );

    return recentMetrics.length % 100 === 0 || this.hasPerformanceDegraded(recentMetrics);
  }

  private hasPerformanceDegraded(metrics: PerformanceMetrics[]): boolean {
    if (metrics.length < 20) return false;
    
    const recent = metrics.slice(-10);
    const older = metrics.slice(-20, -10);
    
    const recentAvgDuration = recent.reduce((sum, m) => sum + m.duration, 0) / recent.length;
    const olderAvgDuration = older.reduce((sum, m) => sum + m.duration, 0) / older.length;
    
    return recentAvgDuration > olderAvgDuration * 1.5; // 50% degradation
  }

  private async getRecentMetrics(agentType: string, taskType: string, limit: number): Promise<PerformanceMetrics[]> {
    const key = `metrics:${agentType}:${taskType}`;
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    
    const metricStrings = await this.redis.zrangebyscore(key, oneHourAgo, now, 'LIMIT', 0, limit);
    return metricStrings.map(str => JSON.parse(str));
  }

  private extractTaskPattern(metric: PerformanceMetrics): string {
    // Simple pattern extraction - could be enhanced with ML
    return `${metric.agentType}:${metric.taskType}`;
  }

  private generateCacheKey(pattern: string): string {
    return `cache:${pattern.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  private groupSimilarTasks(metrics: PerformanceMetrics[]): Map<string, PerformanceMetrics[]> {
    const groups = new Map<string, PerformanceMetrics[]>();
    
    metrics.forEach(metric => {
      const group = this.extractTaskPattern(metric);
      if (!groups.has(group)) {
        groups.set(group, []);
      }
      groups.get(group)!.push(metric);
    });
    
    return groups;
  }

  private async getCurrentInstanceCount(agentType: string): Promise<number> {
    // In a real implementation, this would query Kubernetes API
    return 1; // Placeholder
  }

  private initializeBaselines(): void {
    // Set performance baselines for different task types
    this.performanceBaselines.set('research:job_discovery', 30000); // 30 seconds
    this.performanceBaselines.set('execution:job_application', 120000); // 2 minutes
    this.performanceBaselines.set('resume:optimization', 60000); // 1 minute
    this.performanceBaselines.set('monitoring:status_check', 10000); // 10 seconds
  }

  private startPerformanceMonitoring(): void {
    setInterval(async () => {
      await this.generatePerformanceReport();
    }, 300000); // Every 5 minutes
  }

  private async generatePerformanceReport(): Promise<void> {
    const report = {
      timestamp: new Date(),
      totalMetrics: this.metrics.length,
      optimizationRecommendations: this.optimizationHistory.size,
      performanceTrends: await this.calculatePerformanceTrends()
    };

    await this.redis.setex(
      'performance_report:latest',
      3600, // 1 hour
      JSON.stringify(report)
    );
  }

  private async calculatePerformanceTrends(): Promise<Record<string, any>> {
    const trends: Record<string, any> = {};
    
    for (const [baseline, expectedDuration] of this.performanceBaselines.entries()) {
      const [agentType, taskType] = baseline.split(':');
      const recentMetrics = await this.getRecentMetrics(agentType, taskType, 50);
      
      if (recentMetrics.length > 0) {
        const avgDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
        const trend = ((avgDuration - expectedDuration) / expectedDuration) * 100;
        
        trends[baseline] = {
          expected: expectedDuration,
          actual: avgDuration,
          trend: trend.toFixed(1) + '%',
          status: trend > 20 ? 'degraded' : trend < -10 ? 'improved' : 'stable'
        };
      }
    }
    
    return trends;
  }

  async getOptimizationRecommendations(agentType?: string): Promise<OptimizationRecommendation[]> {
    if (agentType) {
      const recommendations: OptimizationRecommendation[] = [];
      for (const [key, recs] of this.optimizationHistory.entries()) {
        if (key.startsWith(agentType + ':')) {
          recommendations.push(...recs);
        }
      }
      return recommendations;
    }
    
    return Array.from(this.optimizationHistory.values()).flat();
  }
}
