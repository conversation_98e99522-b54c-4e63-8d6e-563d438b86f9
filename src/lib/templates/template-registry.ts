// Template registry for CVLeap resume templates
// This file manages the registration and loading of all template components

import React from 'react';
import { ResumeContent } from '@/types';
import { TemplateMetadata, TemplateComponent } from './template-catalog';

// Import all template components
import ProfessionalSidebarTemplate from '@/components/templates/ProfessionalSidebarTemplate';
import ModernPhotoCVTemplate from '@/components/templates/ModernPhotoCVTemplate';
import CleanProfessionalTemplate from '@/components/templates/CleanProfessionalTemplate';
import TwoColumnSidebarTemplate from '@/components/templates/TwoColumnSidebarTemplate';

// Import newly integrated templates from 1comp directory
import { Classic001Template } from '@/components/templates/Classic001Template';
import CleanTemplate from '@/components/templates/CleanTemplate';
import { CorporateTemplate } from '@/components/templates/CorporateTemplate';
import { CreativeTemplate } from '@/components/templates/CreativeTemplate';
import { DynamicTemplate } from '@/components/templates/DynamicTemplate';
import { ElegantTemplate } from '@/components/templates/ElegantTemplate';
import EssentialTemplate from '@/components/templates/EssentialTemplate';
import { ExecutiveBlueTemplate } from '@/components/templates/ExecutiveBlueTemplate';
import { ExecutiveTemplate } from '@/components/templates/ExecutiveTemplate';
import { Fresh001Template } from '@/components/templates/Fresh001Template';
import { FreshTemplate } from '@/components/templates/FreshTemplate';
import { MinimalTemplate } from '@/components/templates/MinimalTemplate';
import { ModernTealTemplate } from '@/components/templates/ModernTealTemplate';
import { ModernTemplate } from '@/components/templates/ModernTemplate';
import { PlaceholderTemplate } from '@/components/templates/PlaceholderTemplate';
import { ProfessionalTemplate } from '@/components/templates/ProfessionalTemplate';
import { PureTemplate } from '@/components/templates/PureTemplate';
import SimpleTemplate from '@/components/templates/SimpleTemplate';
import { TechnicalBlueTemplate } from '@/components/templates/TechnicalBlueTemplate';

// Import newly created templates (Batch 1: Creative Templates)
import DecorativeHeaderTemplate from '@/components/templates/DecorativeHeaderTemplate';
import CreativeDesignTemplate from '@/components/templates/CreativeDesignTemplate';
import DecorativeFooterTemplate from '@/components/templates/DecorativeFooterTemplate';
import PersonalityTraitsTemplate from '@/components/templates/PersonalityTraitsTemplate';
import GreenSidebarTemplate from '@/components/templates/GreenSidebarTemplate';

// Import newly created templates (Batch 2: Professional Templates)
import ContactBadgesTemplate from '@/components/templates/ContactBadgesTemplate';
import AttorneyResumeTemplate from '@/components/templates/AttorneyResumeTemplate';
import UxDesignerTemplate from '@/components/templates/UxDesignerTemplate';
import TraditionalLayoutTemplate from '@/components/templates/TraditionalLayoutTemplate';
import TimelineStyleTemplate from '@/components/templates/TimelineStyleTemplate';

// Import newly created templates (Batch 3: Named Professional Templates)
import BusinessConsultantTemplate from '@/components/templates/BusinessConsultantTemplate';
import AndrewBoltonDesignerTemplate from '@/components/templates/AndrewBoltonDesignerTemplate';
import DianneRussellUiuxTemplate from '@/components/templates/DianneRussellUiuxTemplate';
import JohnDoeUxuiTemplate from '@/components/templates/JohnDoeUxuiTemplate';
import DannaAlquatiBackendTemplate from '@/components/templates/DannaAlquatiBackendTemplate';

// Import newly created templates (Batch 4: Specialized Templates)
import ProfessionalExecutiveTemplate from '@/components/templates/ProfessionalExecutiveTemplate';
import GraphicDesignerPortfolioTemplate from '@/components/templates/GraphicDesignerPortfolioTemplate';
import CreativeCircularPhotoTemplate from '@/components/templates/CreativeCircularPhotoTemplate';
import DataAnalystProfessionalTemplate from '@/components/templates/DataAnalystProfessionalTemplate';
import SalesExecutiveProfessionalTemplate from '@/components/templates/SalesExecutiveProfessionalTemplate';

// Import newly created templates (Batch 5: Marketing & UX Templates)
import MarketingSpecialistTemplate from '@/components/templates/MarketingSpecialistTemplate';
import CleanSkillsBarsTemplate from '@/components/templates/CleanSkillsBarsTemplate';
import UxDesignerBlueAccentsTemplate from '@/components/templates/UxDesignerBlueAccentsTemplate';
import ProductDesignerTwoColumnTemplate from '@/components/templates/ProductDesignerTwoColumnTemplate';

// Import newly created templates (Batch 6: Portfolio & Project Templates - Remaining)
import UxPhotoSkillsTagsTemplate from '@/components/templates/UxPhotoSkillsTagsTemplate';
import ProjectManagerTemplate from '@/components/templates/ProjectManagerTemplate';

// Import newly created templates (Batch 7: Final Templates)
import BusinessAnalystPhotoTemplate from '@/components/templates/BusinessAnalystPhotoTemplate';
import DigitalMarketingDarkTemplate from '@/components/templates/DigitalMarketingDarkTemplate';
import CreativeDesignerSidebarTemplate from '@/components/templates/CreativeDesignerSidebarTemplate';
import FrontendDeveloperTemplate from '@/components/templates/FrontendDeveloperTemplate';
import ProfessionalSocialAwardsTemplate from '@/components/templates/ProfessionalSocialAwardsTemplate';
import CleanBlueAccentsTemplate from '@/components/templates/CleanBlueAccentsTemplate';

// Import sample templates from other batches
import JamesSmithUxTimelineTemplate from '@/components/templates/JamesSmithUxTimelineTemplate';
import Resume4ProjectsTemplate from '@/components/templates/Resume4ProjectsTemplate';
import UxTimelineSkillsTemplate from '@/components/templates/UxTimelineSkillsTemplate';
import ExperiencePortfolioTemplate from '@/components/templates/ExperiencePortfolioTemplate';

// Template component registry - Maps template IDs to React components
export const TEMPLATE_COMPONENTS: Record<string, React.ComponentType<any>> = {
  // Original templates
  'professional-sidebar-1': ProfessionalSidebarTemplate,
  'modern-photo-cv': ModernPhotoCVTemplate,
  'clean-professional-3': CleanProfessionalTemplate,
  'two-column-sidebar': TwoColumnSidebarTemplate,

  // Newly integrated templates (Templates 42-60)
  'classic-001-template': Classic001Template,
  'clean-template': CleanTemplate,
  'corporate-template': CorporateTemplate,
  'creative-template': CreativeTemplate,
  'dynamic-template': DynamicTemplate,
  'elegant-template': ElegantTemplate,
  'essential-template': EssentialTemplate,
  'executive-blue-template': ExecutiveBlueTemplate,
  'executive-template': ExecutiveTemplate,
  'fresh-001-template': Fresh001Template,
  'fresh-template': FreshTemplate,
  'minimal-template': MinimalTemplate,
  'modern-teal-template': ModernTealTemplate,
  'modern-template': ModernTemplate,
  'placeholder-template': PlaceholderTemplate,
  'professional-template': ProfessionalTemplate,
  'pure-template': PureTemplate,
  'simple-template': SimpleTemplate,
  'technical-blue-template': TechnicalBlueTemplate,

  // Figma-extracted templates - Now with full implementations

  // Batch 1: Creative Templates (IMPLEMENTED)
  'decorative-header-5': DecorativeHeaderTemplate,
  'creative-design-6': CreativeDesignTemplate,
  'decorative-footer-7': DecorativeFooterTemplate,
  'personality-traits-8': PersonalityTraitsTemplate,
  'green-sidebar-9': GreenSidebarTemplate,

  // Batch 2: Professional Templates (IMPLEMENTED)
  'contact-badges-10': ContactBadgesTemplate,
  'attorney-resume-11': AttorneyResumeTemplate,
  'ux-designer-12': UxDesignerTemplate,
  'traditional-layout-13': TraditionalLayoutTemplate,
  'timeline-style-14': TimelineStyleTemplate,

  // Batch 3: Named Professional Templates (IMPLEMENTED)
  'business-consultant': BusinessConsultantTemplate,
  'andrew-bolton-designer': AndrewBoltonDesignerTemplate,
  'dianne-russell-uiux': DianneRussellUiuxTemplate,
  'john-doe-uxui': JohnDoeUxuiTemplate,
  'danna-alquati-backend': DannaAlquatiBackendTemplate,

  // Batch 4: Specialized Templates (IMPLEMENTED)
  'professional-template-21': ProfessionalExecutiveTemplate,
  'anne-harris-graphic': GraphicDesignerPortfolioTemplate,
  'creative-circular-photo': CreativeCircularPhotoTemplate,
  'jason-reyes-analyst': DataAnalystProfessionalTemplate,
  'natasha-wilson-sales': SalesExecutiveProfessionalTemplate,

  // Batch 5: Marketing & UX Templates (IMPLEMENTED)
  'rana-muktyomber-marketing': MarketingSpecialistTemplate,
  'james-smith-ux-timeline': JamesSmithUxTimelineTemplate, // IMPLEMENTED (Sample)
  'clean-skills-bars': CleanSkillsBarsTemplate,
  'john-doe-ux-blue': UxDesignerBlueAccentsTemplate,
  'john-doe-product-two-column': ProductDesignerTwoColumnTemplate,

  // Batch 6: Portfolio & Project Templates (IMPLEMENTED)
  'john-doe-ux-timeline-2': UxTimelineSkillsTemplate,
  'james-smith-experience-portfolio': ExperiencePortfolioTemplate,
  'james-smith-ux-photo': UxPhotoSkillsTagsTemplate,
  'resume-4-projects': Resume4ProjectsTemplate, // IMPLEMENTED (Sample)
  'emma-harrison-pm': ProjectManagerTemplate,

  // Batch 7: Final Templates (IMPLEMENTED)
  'priya-kapoor-analyst': BusinessAnalystPhotoTemplate,
  'rahul-mehta-marketing': DigitalMarketingDarkTemplate,
  'robyn-kingsley-designer': CreativeDesignerSidebarTemplate,
  'aarav-mehta-frontend': FrontendDeveloperTemplate,
  'rukia-sharma-resume': ProfessionalSocialAwardsTemplate,
  'jone-don-resume': CleanBlueAccentsTemplate,
};

// Template registry class for managing templates
export class TemplateRegistry {
  private static instance: TemplateRegistry;
  private templates: Map<string, TemplateComponent> = new Map();

  private constructor() {
    this.initializeTemplates();
  }

  public static getInstance(): TemplateRegistry {
    if (!TemplateRegistry.instance) {
      TemplateRegistry.instance = new TemplateRegistry();
    }
    return TemplateRegistry.instance;
  }

  private initializeTemplates(): void {
    // Register all available templates
    Object.entries(TEMPLATE_COMPONENTS).forEach(([id, component]) => {
      this.registerTemplate(id, component);
    });
  }

  public registerTemplate(id: string, component: React.ComponentType<any>, metadata?: TemplateMetadata): void {
    const templateComponent: TemplateComponent = {
      id,
      component,
      metadata: metadata || this.getMetadataById(id)
    };
    this.templates.set(id, templateComponent);
  }

  public getTemplate(id: string): TemplateComponent | undefined {
    return this.templates.get(id);
  }

  public getAllTemplates(): TemplateComponent[] {
    return Array.from(this.templates.values());
  }

  public getTemplatesByCategory(category: string): TemplateComponent[] {
    return this.getAllTemplates().filter(template => 
      template.metadata?.category === category || category === 'all'
    );
  }

  public searchTemplates(query: string): TemplateComponent[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllTemplates().filter(template => {
      const metadata = template.metadata;
      if (!metadata) return false;
      
      return (
        metadata.name.toLowerCase().includes(lowercaseQuery) ||
        metadata.description.toLowerCase().includes(lowercaseQuery) ||
        metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    });
  }

  public getTemplateComponent(id: string): React.ComponentType<any> | undefined {
    const template = this.getTemplate(id);
    return template?.component;
  }

  private getMetadataById(id: string): TemplateMetadata {
    // Import template catalog to get metadata
    const { TEMPLATE_CATALOG } = require('./template-catalog');
    return TEMPLATE_CATALOG.find((template: TemplateMetadata) => template.id === id) || {
      id,
      name: 'Unknown Template',
      category: 'professional' as const,
      description: 'Template description not available',
      figmaNodeId: '',
      dimensions: { width: 595, height: 842 },
      sections: [],
      colorScheme: {
        primary: '#000000',
        background: '#ffffff',
        text: '#000000'
      },
      layout: 'single-column' as const,
      hasPhoto: false,
      atsOptimized: true,
      isPremium: false,
      tags: []
    };
  }

  public isTemplateAvailable(id: string): boolean {
    return this.templates.has(id);
  }

  public getTemplateIds(): string[] {
    return Array.from(this.templates.keys());
  }
}

// Export singleton instance
export const templateRegistry = TemplateRegistry.getInstance();

// Helper functions for easy access
export function getTemplateComponent(id: string): React.ComponentType<any> | undefined {
  return templateRegistry.getTemplateComponent(id);
}

export function getAllTemplates(): TemplateComponent[] {
  return templateRegistry.getAllTemplates();
}

export function getTemplatesByCategory(category: string): TemplateComponent[] {
  return templateRegistry.getTemplatesByCategory(category);
}

export function searchTemplates(query: string): TemplateComponent[] {
  return templateRegistry.searchTemplates(query);
}

export function isTemplateAvailable(id: string): boolean {
  return templateRegistry.isTemplateAvailable(id);
}

// Re-export ResumeContent from main types for template compatibility
export type { ResumeContent as ResumeData } from '@/types';

// Template props interface
export interface TemplateProps {
  data: ResumeContent;
  isPreview?: boolean;
  className?: string;
}

// Template validation function
export function validateTemplateData(data: ResumeContent, templateId: string): boolean {
  const template = templateRegistry.getTemplate(templateId);
  if (!template || !template.metadata) return false;

  const requiredSections = template.metadata.sections;
  
  // Check if all required sections have data
  for (const section of requiredSections) {
    switch (section) {
      case 'contact':
        if (!data.personalInfo.fullName || !data.personalInfo.email) return false;
        break;
      case 'experience':
        if (!data.experience || data.experience.length === 0) return false;
        break;
      case 'education':
        if (!data.education || data.education.length === 0) return false;
        break;
      case 'skills':
        if (!data.skills || data.skills.length === 0) return false;
        break;
      case 'profile':
      case 'summary':
        if (!data.summary) return false;
        break;
      case 'profile-picture':
        // Photo is optional in our current schema
        break;
    }
  }

  return true;
}
