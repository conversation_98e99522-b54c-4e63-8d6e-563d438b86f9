import { z } from 'zod';

// Environment-specific configuration schema
const AgentConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production']),
  
  // Agent behavior configuration
  agents: z.object({
    planning: z.object({
      maxConcurrentTasks: z.number().default(10),
      decisionThreshold: z.number().min(0).max(1).default(0.7),
      learningRate: z.number().min(0).max(1).default(0.1),
      adaptationEnabled: z.boolean().default(true),
      autonomyLevel: z.enum(['low', 'medium', 'high']).default('medium'),
      goalPersistence: z.number().min(1).max(10).default(5),
    }),
    
    research: z.object({
      maxConcurrentSessions: z.number().default(3),
      dataFreshnessThreshold: z.number().default(3600000), // 1 hour
      confidenceThreshold: z.number().min(0).max(1).default(0.8),
      adaptiveScrapingEnabled: z.boolean().default(true),
      learningFromFailures: z.boolean().default(true),
      proactiveAnalysis: z.boolean().default(true),
    }),
    
    execution: z.object({
      maxBrowserSessions: z.number().default(5),
      sessionTimeout: z.number().default(1800000), // 30 minutes
      antiDetectionLevel: z.enum(['basic', 'advanced', 'stealth']).default('advanced'),
      adaptiveRetryStrategy: z.boolean().default(true),
      humanBehaviorSimulation: z.boolean().default(true),
      contextualDecisionMaking: z.boolean().default(true),
    }),
    
    resumeOptimization: z.object({
      optimizationStrategies: z.array(z.string()).default(['ats', 'keyword', 'readability']),
      learningFromFeedback: z.boolean().default(true),
      adaptiveOptimization: z.boolean().default(true),
      performanceTracking: z.boolean().default(true),
      continuousImprovement: z.boolean().default(true),
    }),
    
    monitoring: z.object({
      monitoringInterval: z.number().default(3600000), // 1 hour
      proactiveFollowUp: z.boolean().default(true),
      sentimentAnalysisEnabled: z.boolean().default(true),
      predictiveAnalytics: z.boolean().default(true),
      adaptiveScheduling: z.boolean().default(true),
    }),
  }),
  
  // AI/ML configuration
  ai: z.object({
    openai: z.object({
      apiKey: z.string(),
      model: z.string().default('gpt-4'),
      temperature: z.number().min(0).max(2).default(0.7),
      maxTokens: z.number().default(2000),
    }),
    
    anthropic: z.object({
      apiKey: z.string(),
      model: z.string().default('claude-3-sonnet-20240229'),
      temperature: z.number().min(0).max(1).default(0.7),
      maxTokens: z.number().default(2000),
    }),
    
    decisionMaking: z.object({
      algorithm: z.enum(['rule-based', 'ml-enhanced', 'neural']).default('ml-enhanced'),
      confidenceThreshold: z.number().min(0).max(1).default(0.75),
      learningEnabled: z.boolean().default(true),
      adaptationRate: z.number().min(0).max(1).default(0.1),
    }),
  }),
  
  // Human handoff configuration
  humanHandoff: z.object({
    enabled: z.boolean().default(true),
    triggers: z.object({
      lowConfidence: z.number().min(0).max(1).default(0.5),
      complexDecision: z.boolean().default(true),
      errorThreshold: z.number().default(3),
      timeoutThreshold: z.number().default(1800000), // 30 minutes
    }),
    
    escalation: z.object({
      channels: z.array(z.enum(['email', 'slack', 'dashboard', 'sms'])).default(['dashboard', 'email']),
      responseTimeout: z.number().default(3600000), // 1 hour
      autoResumeAfterApproval: z.boolean().default(true),
    }),
    
    visibility: z.object({
      realTimeUpdates: z.boolean().default(true),
      detailedLogging: z.boolean().default(true),
      decisionExplanations: z.boolean().default(true),
      auditTrail: z.boolean().default(true),
    }),
  }),
  
  // Performance and scaling
  performance: z.object({
    caching: z.object({
      enabled: z.boolean().default(true),
      ttl: z.number().default(3600), // 1 hour
      strategy: z.enum(['lru', 'lfu', 'ttl']).default('lru'),
    }),
    
    scaling: z.object({
      autoScaling: z.boolean().default(true),
      minInstances: z.number().default(1),
      maxInstances: z.number().default(10),
      targetCpuUtilization: z.number().default(70),
      scaleUpCooldown: z.number().default(300), // 5 minutes
      scaleDownCooldown: z.number().default(600), // 10 minutes
    }),
    
    optimization: z.object({
      batchProcessing: z.boolean().default(true),
      parallelExecution: z.boolean().default(true),
      resourcePooling: z.boolean().default(true),
      intelligentQueuing: z.boolean().default(true),
    }),
  }),
  
  // Security and compliance
  security: z.object({
    encryption: z.object({
      enabled: z.boolean().default(true),
      algorithm: z.string().default('AES-256-GCM'),
      keyRotation: z.boolean().default(true),
    }),
    
    privacy: z.object({
      dataAnonymization: z.boolean().default(true),
      gdprCompliance: z.boolean().default(true),
      dataRetention: z.number().default(2592000000), // 30 days
    }),
    
    access: z.object({
      rbacEnabled: z.boolean().default(true),
      auditLogging: z.boolean().default(true),
      sessionTimeout: z.number().default(3600000), // 1 hour
    }),
  }),
});

export type AgentConfig = z.infer<typeof AgentConfigSchema>;

// Environment-specific configurations
const configurations: Record<string, Partial<AgentConfig>> = {
  development: {
    environment: 'development',
    agents: {
      planning: {
        maxConcurrentTasks: 5,
        autonomyLevel: 'low',
        adaptationEnabled: false,
      },
      research: {
        maxConcurrentSessions: 2,
        adaptiveScrapingEnabled: false,
        proactiveAnalysis: false,
      },
      execution: {
        maxBrowserSessions: 2,
        antiDetectionLevel: 'basic',
        humanBehaviorSimulation: false,
      },
    },
    humanHandoff: {
      triggers: {
        lowConfidence: 0.3,
        errorThreshold: 1,
      },
    },
    performance: {
      scaling: {
        autoScaling: false,
        minInstances: 1,
        maxInstances: 2,
      },
    },
  },
  
  staging: {
    environment: 'staging',
    agents: {
      planning: {
        maxConcurrentTasks: 8,
        autonomyLevel: 'medium',
        adaptationEnabled: true,
      },
      research: {
        maxConcurrentSessions: 3,
        adaptiveScrapingEnabled: true,
        proactiveAnalysis: true,
      },
      execution: {
        maxBrowserSessions: 3,
        antiDetectionLevel: 'advanced',
        humanBehaviorSimulation: true,
      },
    },
    humanHandoff: {
      triggers: {
        lowConfidence: 0.6,
        errorThreshold: 2,
      },
    },
    performance: {
      scaling: {
        autoScaling: true,
        minInstances: 2,
        maxInstances: 5,
      },
    },
  },
  
  production: {
    environment: 'production',
    agents: {
      planning: {
        maxConcurrentTasks: 20,
        autonomyLevel: 'high',
        adaptationEnabled: true,
        learningRate: 0.05, // More conservative in production
      },
      research: {
        maxConcurrentSessions: 5,
        adaptiveScrapingEnabled: true,
        proactiveAnalysis: true,
        learningFromFailures: true,
      },
      execution: {
        maxBrowserSessions: 10,
        antiDetectionLevel: 'stealth',
        humanBehaviorSimulation: true,
        contextualDecisionMaking: true,
      },
    },
    humanHandoff: {
      triggers: {
        lowConfidence: 0.7,
        errorThreshold: 3,
      },
      escalation: {
        responseTimeout: 1800000, // 30 minutes in production
      },
    },
    performance: {
      scaling: {
        autoScaling: true,
        minInstances: 3,
        maxInstances: 20,
        targetCpuUtilization: 60, // More conservative
      },
    },
  },
};

export function getAgentConfig(environment?: string): AgentConfig {
  const env = environment || process.env.NODE_ENV || 'development';
  const baseConfig = configurations[env] || configurations.development;
  
  // Merge with environment variables
  const config = {
    ...baseConfig,
    ai: {
      openai: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: process.env.OPENAI_MODEL || 'gpt-4',
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY || '',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
        temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '2000'),
      },
      decisionMaking: {
        algorithm: (process.env.DECISION_ALGORITHM as any) || 'ml-enhanced',
        confidenceThreshold: parseFloat(process.env.CONFIDENCE_THRESHOLD || '0.75'),
        learningEnabled: process.env.LEARNING_ENABLED !== 'false',
        adaptationRate: parseFloat(process.env.ADAPTATION_RATE || '0.1'),
      },
    },
  };
  
  return AgentConfigSchema.parse(config);
}

export const agentConfig = getAgentConfig();
