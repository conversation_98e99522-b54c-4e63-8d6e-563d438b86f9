#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createServer } from 'http';
import { Redis } from 'ioredis';
import winston from 'winston';
import { AgentOrchestrator } from './core/agent-orchestrator';
import { PlanningAgent } from './specialized/planning-agent';
import { ResearchAgent } from './specialized/research-agent';
import { ExecutionAgent } from './specialized/execution-agent';
import { ResumeOptimizationAgent } from './specialized/resume-optimization-agent';
import { MonitoringAgent } from './specialized/monitoring-agent';
import { prisma } from '../lib/db';

// Environment configuration
const PORT = parseInt(process.env.PORT || '8080');
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const NODE_ENV = process.env.NODE_ENV || 'development';
const LOG_LEVEL = process.env.LOG_LEVEL || 'info';

// Initialize logger
const logger = winston.createLogger({
  level: LOG_LEVEL,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'cvleap-agent-orchestrator' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Add file logging in production
if (NODE_ENV === 'production') {
  logger.add(new winston.transports.File({
    filename: '/var/log/cvleap/error.log',
    level: 'error'
  }));
  logger.add(new winston.transports.File({
    filename: '/var/log/cvleap/combined.log'
  }));
}

class AgentApplication {
  private app: express.Application;
  private server: any;
  private redis: Redis;
  private orchestrator: AgentOrchestrator;
  private agents: Map<string, any> = new Map();
  private isShuttingDown = false;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.headers['x-request-id']
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoints
    this.app.get('/health', this.healthCheck.bind(this));
    this.app.get('/ready', this.readinessCheck.bind(this));
    this.app.get('/metrics', this.metricsEndpoint.bind(this));

    // Agent management endpoints
    this.app.get('/agents', this.getAgentStatus.bind(this));
    this.app.post('/agents/:agentType/tasks', this.createTask.bind(this));
    this.app.get('/agents/:agentType/tasks/:taskId', this.getTaskStatus.bind(this));
    this.app.delete('/agents/:agentType/tasks/:taskId', this.cancelTask.bind(this));

    // Orchestrator endpoints
    this.app.post('/orchestrator/pipelines', this.createPipeline.bind(this));
    this.app.get('/orchestrator/pipelines/:pipelineId', this.getPipelineStatus.bind(this));
    this.app.post('/orchestrator/pipelines/:pipelineId/pause', this.pausePipeline.bind(this));
    this.app.post('/orchestrator/pipelines/:pipelineId/resume', this.resumePipeline.bind(this));

    // System management endpoints
    this.app.post('/system/shutdown', this.gracefulShutdown.bind(this));
    this.app.get('/system/status', this.systemStatus.bind(this));

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
      });
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      if (res.headersSent) {
        return next(err);
      }

      const statusCode = err.statusCode || err.status || 500;
      const message = NODE_ENV === 'production' && statusCode === 500 
        ? 'Internal Server Error' 
        : err.message;

      res.status(statusCode).json({
        error: 'Server Error',
        message,
        timestamp: new Date().toISOString(),
        ...(NODE_ENV === 'development' && { stack: err.stack })
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      logger.error('Uncaught Exception:', err);
      this.gracefulShutdown();
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown();
    });
  }

  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing CVLeap Agent System...');

      // Initialize Redis connection
      this.redis = new Redis(REDIS_URL, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      await this.redis.connect();
      logger.info('Redis connection established');

      // Test database connection
      await prisma.$connect();
      logger.info('Database connection established');

      // Initialize orchestrator
      this.orchestrator = new AgentOrchestrator(this.redis, logger);
      await this.orchestrator.initialize();

      // Initialize and register agents
      await this.initializeAgents();

      logger.info('CVLeap Agent System initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize agent system:', error);
      throw error;
    }
  }

  private async initializeAgents(): Promise<void> {
    try {
      // Initialize all agent types
      const planningAgent = new PlanningAgent(this.redis, logger);
      const researchAgent = new ResearchAgent(this.redis, logger);
      const executionAgent = new ExecutionAgent(this.redis, logger);
      const resumeOptimizationAgent = new ResumeOptimizationAgent(this.redis, logger);
      const monitoringAgent = new MonitoringAgent(this.redis, logger);

      // Register agents with orchestrator
      await this.orchestrator.registerAgent(planningAgent);
      await this.orchestrator.registerAgent(researchAgent);
      await this.orchestrator.registerAgent(executionAgent);
      await this.orchestrator.registerAgent(resumeOptimizationAgent);
      await this.orchestrator.registerAgent(monitoringAgent);

      // Store agent references
      this.agents.set('planning', planningAgent);
      this.agents.set('research', researchAgent);
      this.agents.set('execution', executionAgent);
      this.agents.set('resume-optimization', resumeOptimizationAgent);
      this.agents.set('monitoring', monitoringAgent);

      logger.info(`Initialized ${this.agents.size} agents`);

    } catch (error) {
      logger.error('Failed to initialize agents:', error);
      throw error;
    }
  }

  public async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = createServer(this.app);

      this.server.listen(PORT, () => {
        logger.info(`CVLeap Agent System listening on port ${PORT}`);
        logger.info(`Environment: ${NODE_ENV}`);
        logger.info(`Health check: http://localhost:${PORT}/health`);
        resolve();
      });

      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          logger.error(`Port ${PORT} is already in use`);
        } else {
          logger.error('Server error:', error);
        }
        reject(error);
      });

      // Graceful shutdown handlers
      process.on('SIGTERM', () => {
        logger.info('SIGTERM received, starting graceful shutdown');
        this.gracefulShutdown();
      });

      process.on('SIGINT', () => {
        logger.info('SIGINT received, starting graceful shutdown');
        this.gracefulShutdown();
      });
    });
  }

  // Health check endpoint
  private async healthCheck(req: express.Request, res: express.Response): Promise<void> {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: NODE_ENV,
        checks: {
          redis: await this.checkRedisHealth(),
          database: await this.checkDatabaseHealth(),
          agents: await this.checkAgentsHealth()
        }
      };

      const allHealthy = Object.values(health.checks).every(check => check.status === 'healthy');
      const statusCode = allHealthy ? 200 : 503;

      res.status(statusCode).json(health);

    } catch (error) {
      logger.error('Health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Readiness check endpoint
  private async readinessCheck(req: express.Request, res: express.Response): Promise<void> {
    try {
      if (this.isShuttingDown) {
        return res.status(503).json({
          status: 'not_ready',
          reason: 'shutting_down',
          timestamp: new Date().toISOString()
        });
      }

      const ready = {
        status: 'ready',
        timestamp: new Date().toISOString(),
        agents: {
          total: this.agents.size,
          active: Array.from(this.agents.values()).filter(agent => agent.isActive()).length
        },
        orchestrator: {
          status: this.orchestrator ? 'active' : 'inactive',
          activePipelines: this.orchestrator ? await this.orchestrator.getActivePipelineCount() : 0
        }
      };

      res.status(200).json(ready);

    } catch (error) {
      logger.error('Readiness check failed:', error);
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Metrics endpoint for Prometheus
  private async metricsEndpoint(req: express.Request, res: express.Response): Promise<void> {
    try {
      const metrics = await this.collectMetrics();
      
      // Return metrics in Prometheus format
      let prometheusMetrics = '';
      
      for (const [name, value] of Object.entries(metrics)) {
        prometheusMetrics += `# HELP ${name} ${name.replace(/_/g, ' ')}\n`;
        prometheusMetrics += `# TYPE ${name} gauge\n`;
        prometheusMetrics += `${name} ${value}\n\n`;
      }

      res.set('Content-Type', 'text/plain');
      res.send(prometheusMetrics);

    } catch (error) {
      logger.error('Metrics collection failed:', error);
      res.status(500).json({ error: 'Failed to collect metrics' });
    }
  }

  private async collectMetrics(): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {};

    // System metrics
    metrics.cvleap_agent_uptime_seconds = process.uptime();
    metrics.cvleap_agent_memory_usage_bytes = process.memoryUsage().heapUsed;
    metrics.cvleap_agent_cpu_usage_percent = process.cpuUsage().user / 1000000; // Convert to seconds

    // Agent metrics
    metrics.cvleap_agents_total = this.agents.size;
    metrics.cvleap_agents_active = Array.from(this.agents.values()).filter(agent => agent.isActive()).length;

    // Orchestrator metrics
    if (this.orchestrator) {
      metrics.cvleap_pipelines_active = await this.orchestrator.getActivePipelineCount();
      metrics.cvleap_tasks_queued = await this.orchestrator.getQueuedTaskCount();
      metrics.cvleap_tasks_completed_total = await this.orchestrator.getCompletedTaskCount();
    }

    // Redis metrics
    const redisInfo = await this.redis.info('memory');
    const memoryMatch = redisInfo.match(/used_memory:(\d+)/);
    if (memoryMatch) {
      metrics.cvleap_redis_memory_bytes = parseInt(memoryMatch[1]);
    }

    return metrics;
  }

  private async checkRedisHealth(): Promise<any> {
    try {
      await this.redis.ping();
      return { status: 'healthy', latency: 0 };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  private async checkDatabaseHealth(): Promise<any> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy' };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  private async checkAgentsHealth(): Promise<any> {
    try {
      const agentStatuses = await Promise.all(
        Array.from(this.agents.values()).map(async agent => ({
          name: agent.name,
          active: agent.isActive(),
          taskCount: await agent.getActiveTaskCount()
        }))
      );

      const healthyAgents = agentStatuses.filter(agent => agent.active).length;
      
      return {
        status: healthyAgents === this.agents.size ? 'healthy' : 'degraded',
        total: this.agents.size,
        healthy: healthyAgents,
        agents: agentStatuses
      };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Agent management endpoints
  private async getAgentStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const agentStatuses = await Promise.all(
        Array.from(this.agents.entries()).map(async ([type, agent]) => ({
          type,
          name: agent.name,
          active: agent.isActive(),
          capabilities: agent.getCapabilities(),
          activeTaskCount: await agent.getActiveTaskCount(),
          totalTasksProcessed: await agent.getTotalTasksProcessed()
        }))
      );

      res.json({
        agents: agentStatuses,
        orchestrator: {
          active: !!this.orchestrator,
          activePipelines: this.orchestrator ? await this.orchestrator.getActivePipelineCount() : 0
        }
      });

    } catch (error) {
      logger.error('Failed to get agent status:', error);
      res.status(500).json({ error: 'Failed to get agent status' });
    }
  }

  private async createTask(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { agentType } = req.params;
      const { taskType, data, priority = 'medium' } = req.body;

      const agent = this.agents.get(agentType);
      if (!agent) {
        return res.status(404).json({ error: `Agent type '${agentType}' not found` });
      }

      const task = await agent.createTask({
        type: taskType,
        data,
        priority,
        userId: req.body.userId
      });

      res.status(201).json({
        taskId: task.id,
        status: task.status,
        estimatedDuration: task.estimatedDuration,
        createdAt: task.createdAt
      });

    } catch (error) {
      logger.error('Failed to create task:', error);
      res.status(500).json({ error: 'Failed to create task' });
    }
  }

  private async getTaskStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { agentType, taskId } = req.params;

      const agent = this.agents.get(agentType);
      if (!agent) {
        return res.status(404).json({ error: `Agent type '${agentType}' not found` });
      }

      const task = await agent.getTask(taskId);
      if (!task) {
        return res.status(404).json({ error: `Task '${taskId}' not found` });
      }

      res.json(task);

    } catch (error) {
      logger.error('Failed to get task status:', error);
      res.status(500).json({ error: 'Failed to get task status' });
    }
  }

  private async cancelTask(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { agentType, taskId } = req.params;

      const agent = this.agents.get(agentType);
      if (!agent) {
        return res.status(404).json({ error: `Agent type '${agentType}' not found` });
      }

      await agent.cancelTask(taskId);
      res.status(204).send();

    } catch (error) {
      logger.error('Failed to cancel task:', error);
      res.status(500).json({ error: 'Failed to cancel task' });
    }
  }

  // Orchestrator endpoints
  private async createPipeline(req: express.Request, res: express.Response): Promise<void> {
    try {
      const pipeline = await this.orchestrator.createPipeline(req.body);
      res.status(201).json(pipeline);

    } catch (error) {
      logger.error('Failed to create pipeline:', error);
      res.status(500).json({ error: 'Failed to create pipeline' });
    }
  }

  private async getPipelineStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { pipelineId } = req.params;
      const pipeline = await this.orchestrator.getPipeline(pipelineId);
      
      if (!pipeline) {
        return res.status(404).json({ error: `Pipeline '${pipelineId}' not found` });
      }

      res.json(pipeline);

    } catch (error) {
      logger.error('Failed to get pipeline status:', error);
      res.status(500).json({ error: 'Failed to get pipeline status' });
    }
  }

  private async pausePipeline(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { pipelineId } = req.params;
      await this.orchestrator.pausePipeline(pipelineId);
      res.status(204).send();

    } catch (error) {
      logger.error('Failed to pause pipeline:', error);
      res.status(500).json({ error: 'Failed to pause pipeline' });
    }
  }

  private async resumePipeline(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { pipelineId } = req.params;
      await this.orchestrator.resumePipeline(pipelineId);
      res.status(204).send();

    } catch (error) {
      logger.error('Failed to resume pipeline:', error);
      res.status(500).json({ error: 'Failed to resume pipeline' });
    }
  }

  private async systemStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const status = {
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          version: process.env.npm_package_version || '1.0.0',
          environment: NODE_ENV
        },
        agents: await this.checkAgentsHealth(),
        orchestrator: {
          active: !!this.orchestrator,
          activePipelines: this.orchestrator ? await this.orchestrator.getActivePipelineCount() : 0,
          queuedTasks: this.orchestrator ? await this.orchestrator.getQueuedTaskCount() : 0
        },
        infrastructure: {
          redis: await this.checkRedisHealth(),
          database: await this.checkDatabaseHealth()
        }
      };

      res.json(status);

    } catch (error) {
      logger.error('Failed to get system status:', error);
      res.status(500).json({ error: 'Failed to get system status' });
    }
  }

  private async gracefulShutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    logger.info('Starting graceful shutdown...');

    try {
      // Stop accepting new connections
      if (this.server) {
        this.server.close();
      }

      // Shutdown orchestrator
      if (this.orchestrator) {
        await this.orchestrator.shutdown();
      }

      // Shutdown all agents
      await Promise.all(
        Array.from(this.agents.values()).map(agent => agent.shutdown())
      );

      // Close Redis connection
      if (this.redis) {
        await this.redis.quit();
      }

      // Close database connection
      await prisma.$disconnect();

      logger.info('Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      logger.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  }
}

// Main execution
async function main() {
  const app = new AgentApplication();

  try {
    await app.initialize();
    await app.start();
  } catch (error) {
    logger.error('Failed to start application:', error);
    process.exit(1);
  }
}

// Start the application
if (require.main === module) {
  main().catch((error) => {
    logger.error('Unhandled error in main:', error);
    process.exit(1);
  });
}

export { AgentApplication };
