import { Page } from 'puppeteer';
import { Logger } from 'winston';

export interface ApplicationData {
  userId: string;
  jobId: string;
  jobUrl: string;
  resumeData: any;
  coverLetterData: any;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    linkedinUrl?: string;
    portfolioUrl?: string;
  };
  applicationAnswers: { [key: string]: string };
  portalType: string;
}

export interface SubmissionResult {
  success: boolean;
  applicationId?: string;
  confirmationNumber?: string;
  submissionUrl?: string;
  error?: string;
  nextSteps?: string[];
}

export abstract class JobPortalAdapter {
  protected logger: Logger;
  protected portalName: string;

  constructor(portalName: string, logger: Logger) {
    this.portalName = portalName;
    this.logger = logger;
  }

  abstract submitApplication(page: Page, data: ApplicationData): Promise<SubmissionResult>;
  abstract detectPortalType(page: Page): Promise<boolean>;
  abstract handleAuthentication(page: Page, credentials: any): Promise<boolean>;

  protected async waitForElement(page: Page, selector: string, timeout: number = 10000): Promise<boolean> {
    try {
      await page.waitForSelector(selector, { timeout });
      return true;
    } catch (error) {
      this.logger.warn(`Element not found: ${selector}`);
      return false;
    }
  }

  protected async safeClick(page: Page, selector: string): Promise<boolean> {
    try {
      await page.click(selector);
      return true;
    } catch (error) {
      this.logger.error(`Failed to click element: ${selector} - ${error}`);
      return false;
    }
  }

  protected async safeType(page: Page, selector: string, text: string): Promise<boolean> {
    try {
      await page.type(selector, text);
      return true;
    } catch (error) {
      this.logger.error(`Failed to type in element: ${selector} - ${error}`);
      return false;
    }
  }

  protected async uploadFile(page: Page, selector: string, filePath: string): Promise<boolean> {
    try {
      const input = await page.$(selector);
      if (input) {
        await input.uploadFile(filePath);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error}`);
      return false;
    }
  }
}

export class LinkedInAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('LinkedIn', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    const url = page.url();
    return url.includes('linkedin.com');
  }

  async handleAuthentication(page: Page, credentials: any): Promise<boolean> {
    try {
      // Check if already logged in
      const profileButton = await page.$('[data-test-id="nav-profile-photo"]');
      if (profileButton) {
        return true; // Already authenticated
      }

      // Navigate to login page
      await page.goto('https://www.linkedin.com/login');
      
      // Fill login form
      await this.safeType(page, '#username', credentials.email);
      await this.safeType(page, '#password', credentials.password);
      await this.safeClick(page, '[type="submit"]');
      
      // Wait for redirect
      await page.waitForNavigation({ waitUntil: 'networkidle0' });
      
      // Verify login success
      const profileButtonAfterLogin = await page.$('[data-test-id="nav-profile-photo"]');
      return !!profileButtonAfterLogin;
      
    } catch (error) {
      this.logger.error(`LinkedIn authentication failed: ${error}`);
      return false;
    }
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<SubmissionResult> {
    try {
      // Navigate to job posting
      await page.goto(data.jobUrl, { waitUntil: 'networkidle0' });
      
      // Look for Easy Apply button
      const easyApplyButton = await page.$('[data-test-id="jobs-apply-button"]');
      if (!easyApplyButton) {
        return {
          success: false,
          error: 'Easy Apply button not found'
        };
      }

      await this.safeClick(page, '[data-test-id="jobs-apply-button"]');
      
      // Wait for application modal
      await this.waitForElement(page, '[data-test-id="jobs-apply-form"]');
      
      // Fill application form
      await this.fillLinkedInForm(page, data);
      
      // Submit application
      const submitButton = await page.$('[data-test-id="jobs-apply-form-submit-button"]');
      if (submitButton) {
        await submitButton.click();
        
        // Wait for confirmation
        await this.waitForElement(page, '[data-test-id="application-submitted"]', 15000);
        
        return {
          success: true,
          applicationId: `linkedin_${Date.now()}`,
          nextSteps: [
            'Application submitted to LinkedIn',
            'Check your email for confirmation',
            'Monitor application status in LinkedIn'
          ]
        };
      }
      
      return {
        success: false,
        error: 'Submit button not found'
      };
      
    } catch (error) {
      this.logger.error(`LinkedIn application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async fillLinkedInForm(page: Page, data: ApplicationData): Promise<void> {
    // Fill phone number if requested
    const phoneInput = await page.$('input[name="phoneNumber"]');
    if (phoneInput) {
      await this.safeType(page, 'input[name="phoneNumber"]', data.personalInfo.phone);
    }

    // Handle cover letter
    const coverLetterTextarea = await page.$('textarea[name="coverLetter"]');
    if (coverLetterTextarea && data.coverLetterData?.content) {
      await this.safeType(page, 'textarea[name="coverLetter"]', data.coverLetterData.content);
    }

    // Handle additional questions
    for (const [question, answer] of Object.entries(data.applicationAnswers)) {
      const questionElements = await page.$$('label');
      for (const element of questionElements) {
        const text = await element.evaluate(el => el.textContent);
        if (text && text.toLowerCase().includes(question.toLowerCase())) {
          const input = await element.$('input, textarea, select');
          if (input) {
            const tagName = await input.evaluate(el => el.tagName.toLowerCase());
            if (tagName === 'select') {
              await input.select(answer);
            } else {
              await input.type(answer);
            }
          }
        }
      }
    }
  }
}

export class WorkdayAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('Workday', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    const url = page.url();
    return url.includes('myworkdayjobs.com') || url.includes('workday.com');
  }

  async handleAuthentication(page: Page, credentials: any): Promise<boolean> {
    // Workday typically doesn't require authentication for job applications
    return true;
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<SubmissionResult> {
    try {
      // Navigate to job posting
      await page.goto(data.jobUrl, { waitUntil: 'networkidle0' });
      
      // Look for Apply button
      const applyButton = await page.$('[data-automation-id="applyButton"]');
      if (!applyButton) {
        return {
          success: false,
          error: 'Apply button not found'
        };
      }

      await this.safeClick(page, '[data-automation-id="applyButton"]');
      
      // Wait for application form
      await this.waitForElement(page, '[data-automation-id="applicationForm"]');
      
      // Fill multi-step Workday form
      await this.fillWorkdayForm(page, data);
      
      return {
        success: true,
        applicationId: `workday_${Date.now()}`,
        nextSteps: [
          'Application submitted through Workday',
          'Check your email for confirmation',
          'Application will be reviewed by hiring team'
        ]
      };
      
    } catch (error) {
      this.logger.error(`Workday application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async fillWorkdayForm(page: Page, data: ApplicationData): Promise<void> {
    // Workday forms are typically multi-step
    let currentStep = 1;
    const maxSteps = 10;

    while (currentStep <= maxSteps) {
      // Fill current step
      await this.fillCurrentStep(page, data, currentStep);
      
      // Look for Next button
      const nextButton = await page.$('[data-automation-id="nextButton"]');
      if (nextButton) {
        await nextButton.click();
        await page.waitForTimeout(2000); // Wait for page transition
        currentStep++;
      } else {
        // Look for Submit button
        const submitButton = await page.$('[data-automation-id="submitButton"]');
        if (submitButton) {
          await submitButton.click();
          break;
        } else {
          break; // No more steps
        }
      }
    }
  }

  private async fillCurrentStep(page: Page, data: ApplicationData, step: number): Promise<void> {
    // Fill personal information
    await this.safeType(page, 'input[data-automation-id="firstName"]', data.personalInfo.firstName);
    await this.safeType(page, 'input[data-automation-id="lastName"]', data.personalInfo.lastName);
    await this.safeType(page, 'input[data-automation-id="email"]', data.personalInfo.email);
    await this.safeType(page, 'input[data-automation-id="phone"]', data.personalInfo.phone);

    // Handle file uploads
    const resumeUpload = await page.$('input[data-automation-id="resumeUpload"]');
    if (resumeUpload && data.resumeData?.filePath) {
      await this.uploadFile(page, 'input[data-automation-id="resumeUpload"]', data.resumeData.filePath);
    }

    const coverLetterUpload = await page.$('input[data-automation-id="coverLetterUpload"]');
    if (coverLetterUpload && data.coverLetterData?.filePath) {
      await this.uploadFile(page, 'input[data-automation-id="coverLetterUpload"]', data.coverLetterData.filePath);
    }

    // Handle dropdown selections and checkboxes
    const dropdowns = await page.$$('select[data-automation-id]');
    for (const dropdown of dropdowns) {
      const automationId = await dropdown.evaluate(el => el.getAttribute('data-automation-id'));
      if (automationId && data.applicationAnswers[automationId]) {
        await dropdown.select(data.applicationAnswers[automationId]);
      }
    }
  }
}

export class GreenhouseAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('Greenhouse', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    const url = page.url();
    return url.includes('greenhouse.io') || url.includes('boards.greenhouse.io');
  }

  async handleAuthentication(page: Page, credentials: any): Promise<boolean> {
    // Greenhouse typically doesn't require authentication for applications
    return true;
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<SubmissionResult> {
    try {
      await page.goto(data.jobUrl, { waitUntil: 'networkidle0' });
      
      // Look for Apply button
      const applyButton = await page.$('#apply_button, .application-button');
      if (!applyButton) {
        return {
          success: false,
          error: 'Apply button not found'
        };
      }

      await this.safeClick(page, '#apply_button, .application-button');
      
      // Fill Greenhouse form
      await this.fillGreenhouseForm(page, data);
      
      // Submit application
      const submitButton = await page.$('input[type="submit"], button[type="submit"]');
      if (submitButton) {
        await submitButton.click();
        
        // Wait for confirmation
        await this.waitForElement(page, '.application-confirmation', 15000);
        
        return {
          success: true,
          applicationId: `greenhouse_${Date.now()}`,
          nextSteps: [
            'Application submitted through Greenhouse',
            'You will receive an email confirmation',
            'Hiring team will review your application'
          ]
        };
      }
      
      return {
        success: false,
        error: 'Submit button not found'
      };
      
    } catch (error) {
      this.logger.error(`Greenhouse application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async fillGreenhouseForm(page: Page, data: ApplicationData): Promise<void> {
    // Fill basic information
    await this.safeType(page, 'input[name="first_name"]', data.personalInfo.firstName);
    await this.safeType(page, 'input[name="last_name"]', data.personalInfo.lastName);
    await this.safeType(page, 'input[name="email"]', data.personalInfo.email);
    await this.safeType(page, 'input[name="phone"]', data.personalInfo.phone);

    // Handle resume upload
    const resumeInput = await page.$('input[name="resume"]');
    if (resumeInput && data.resumeData?.filePath) {
      await this.uploadFile(page, 'input[name="resume"]', data.resumeData.filePath);
    }

    // Handle cover letter
    const coverLetterTextarea = await page.$('textarea[name="cover_letter"]');
    if (coverLetterTextarea && data.coverLetterData?.content) {
      await this.safeType(page, 'textarea[name="cover_letter"]', data.coverLetterData.content);
    }

    // Handle custom questions
    const customQuestions = await page.$$('.custom-question');
    for (const question of customQuestions) {
      const label = await question.$('label');
      const input = await question.$('input, textarea, select');
      
      if (label && input) {
        const labelText = await label.evaluate(el => el.textContent);
        if (labelText) {
          const answer = data.applicationAnswers[labelText.trim()];
          if (answer) {
            const tagName = await input.evaluate(el => el.tagName.toLowerCase());
            if (tagName === 'select') {
              await input.select(answer);
            } else {
              await input.type(answer);
            }
          }
        }
      }
    }
  }
}

export class PortalAdapterFactory {
  private static adapters: Map<string, new (logger: Logger) => JobPortalAdapter> = new Map([
    ['linkedin', LinkedInAdapter],
    ['workday', WorkdayAdapter],
    ['greenhouse', GreenhouseAdapter],
    ['ibm', IBMPortalAdapter],
    ['sap', SAPPortalAdapter],
    ['oracle', OraclePortalAdapter]
  ]);

  static createAdapter(portalType: string, logger: Logger): JobPortalAdapter | null {
    const AdapterClass = this.adapters.get(portalType.toLowerCase());
    return AdapterClass ? new AdapterClass(logger) : null;
  }

  static async detectPortalType(page: Page, logger: Logger): Promise<string | null> {
    for (const [type, AdapterClass] of this.adapters) {
      const adapter = new AdapterClass(logger);
      if (await adapter.detectPortalType(page)) {
        return type;
      }
    }
    return null;
  }

  static getSupportedPortals(): string[] {
    return Array.from(this.adapters.keys());
  }
}

// IBM Careers Portal Adapter
export class IBMPortalAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('IBM Careers', 'https://careers.ibm.com', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    try {
      const url = page.url();
      return url.includes('careers.ibm.com') || url.includes('ibm.com/careers');
    } catch {
      return false;
    }
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<ApplicationResult> {
    try {
      await this.navigateToJob(page, data.jobUrl);
      await this.handleIBMLogin(page, data.credentials);
      await this.fillIBMApplication(page, data);
      await this.submitIBMForm(page);

      return {
        success: true,
        applicationId: await this.extractApplicationId(page),
        submittedAt: new Date(),
        confirmationNumber: await this.extractConfirmationNumber(page)
      };
    } catch (error) {
      this.logger.error(`IBM application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        submittedAt: new Date()
      };
    }
  }

  private async handleIBMLogin(page: Page, credentials: any): Promise<void> {
    // Check if already logged in
    const loginButton = await page.$('button[data-testid="login-button"], a[href*="login"]');
    if (!loginButton) return;

    await loginButton.click();
    await page.waitForSelector('#email, #username', { timeout: 10000 });

    const emailField = await page.$('#email') || await page.$('#username');
    const passwordField = await page.$('#password');

    if (emailField && passwordField) {
      await emailField.type(credentials.email);
      await passwordField.type(credentials.password);

      const submitButton = await page.$('button[type="submit"], input[type="submit"]');
      if (submitButton) {
        await submitButton.click();
        await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
      }
    }
  }

  private async fillIBMApplication(page: Page, data: ApplicationData): Promise<void> {
    // Wait for application form to load
    await page.waitForSelector('.application-form, form', { timeout: 15000 });

    // Fill personal information
    await this.fillPersonalInfo(page, data.personalInfo);

    // Upload resume
    await this.uploadResume(page, data.resumePath);

    // Fill IBM-specific questions
    await this.fillIBMSpecificQuestions(page, data);
  }

  private async fillIBMSpecificQuestions(page: Page, data: ApplicationData): Promise<void> {
    const questions = await page.$$('.application-question, .form-question');

    for (const question of questions) {
      try {
        const questionText = await question.$eval('.question-text, label, .question-label', el => el.textContent?.trim());
        if (!questionText) continue;

        const answer = this.generateIBMAnswer(questionText, data);
        const inputType = await this.detectInputType(question);
        await this.fillQuestionAnswer(question, answer, inputType);
      } catch (error) {
        this.logger.warn(`Failed to process IBM question: ${error}`);
      }
    }
  }

  private generateIBMAnswer(questionText: string, data: ApplicationData): string {
    const lowerQuestion = questionText.toLowerCase();

    if (lowerQuestion.includes('why ibm')) {
      return 'I am excited about IBM\'s leadership in AI and cloud computing, and I believe my skills align well with IBM\'s innovative culture and commitment to technological advancement.';
    }

    if (lowerQuestion.includes('relocation') || lowerQuestion.includes('relocate')) {
      return data.personalInfo.willingToRelocate ? 'Yes' : 'No';
    }

    if (lowerQuestion.includes('salary') || lowerQuestion.includes('compensation')) {
      return data.salaryExpectation || 'Negotiable based on role and responsibilities';
    }

    if (lowerQuestion.includes('security clearance')) {
      return data.securityClearance || 'No, but willing to obtain if required';
    }

    if (lowerQuestion.includes('work authorization') || lowerQuestion.includes('authorized to work')) {
      return data.workAuthorization || 'Yes';
    }

    return 'Yes'; // Default positive response
  }

  private async submitIBMForm(page: Page): Promise<void> {
    const submitButton = await page.$('button[type="submit"], input[value*="Submit"], button:contains("Submit Application")');
    if (submitButton) {
      await submitButton.click();
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
    } else {
      throw new Error('Submit button not found');
    }
  }
}

// SAP Careers Portal Adapter
export class SAPPortalAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('SAP Careers', 'https://jobs.sap.com', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    try {
      const url = page.url();
      return url.includes('jobs.sap.com') || url.includes('sap.com/careers');
    } catch {
      return false;
    }
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<ApplicationResult> {
    try {
      await this.navigateToJob(page, data.jobUrl);
      await this.handleSAPLogin(page, data.credentials);
      await this.fillSAPApplication(page, data);
      await this.submitSAPForm(page);

      return {
        success: true,
        applicationId: await this.extractApplicationId(page),
        submittedAt: new Date(),
        confirmationNumber: await this.extractConfirmationNumber(page)
      };
    } catch (error) {
      this.logger.error(`SAP application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        submittedAt: new Date()
      };
    }
  }

  private async handleSAPLogin(page: Page, credentials: any): Promise<void> {
    const signInButton = await page.$('a[href*="login"], button:contains("Sign In")');
    if (!signInButton) return;

    await signInButton.click();
    await page.waitForSelector('#j_username, #username', { timeout: 10000 });

    const usernameField = await page.$('#j_username') || await page.$('#username');
    const passwordField = await page.$('#j_password') || await page.$('#password');

    if (usernameField && passwordField) {
      await usernameField.type(credentials.email);
      await passwordField.type(credentials.password);

      const loginButton = await page.$('button[name="login"], input[type="submit"]');
      if (loginButton) {
        await loginButton.click();
        await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
      }
    }
  }

  private async fillSAPApplication(page: Page, data: ApplicationData): Promise<void> {
    // SAP uses a multi-step application process
    await this.fillSAPPersonalInfo(page, data.personalInfo);
    await this.fillSAPExperience(page, data.experience);
    await this.uploadSAPDocuments(page, data);
    await this.fillSAPSpecificQuestions(page, data);
  }

  private async fillSAPPersonalInfo(page: Page, personalInfo: any): Promise<void> {
    const fields = [
      { selector: '#firstName, input[name*="firstName"]', value: personalInfo.firstName },
      { selector: '#lastName, input[name*="lastName"]', value: personalInfo.lastName },
      { selector: '#email, input[name*="email"]', value: personalInfo.email },
      { selector: '#phone, input[name*="phone"]', value: personalInfo.phone }
    ];

    for (const field of fields) {
      await this.safeType(page, field.selector, field.value);
    }
  }

  private async fillSAPExperience(page: Page, experience: any[]): Promise<void> {
    for (const exp of experience.slice(0, 3)) { // Limit to top 3 experiences
      try {
        const addButton = await page.$('button:contains("Add Experience"), .add-experience');
        if (addButton) {
          await addButton.click();
          await page.waitForSelector('.experience-form, .work-experience', { timeout: 5000 });

          await this.safeType(page, 'input[name*="company"]', exp.company);
          await this.safeType(page, 'input[name*="title"]', exp.title);
          await this.safeType(page, 'input[name*="startDate"]', exp.startDate);
          await this.safeType(page, 'input[name*="endDate"]', exp.endDate);

          const saveButton = await page.$('button:contains("Save"), input[value="Save"]');
          if (saveButton) {
            await saveButton.click();
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to add SAP experience entry: ${error}`);
      }
    }
  }

  private async uploadSAPDocuments(page: Page, data: ApplicationData): Promise<void> {
    if (data.resumePath) {
      const resumeUpload = await page.$('input[type="file"][accept*="pdf"], input[type="file"][name*="resume"]');
      if (resumeUpload) {
        await resumeUpload.uploadFile(data.resumePath);
      }
    }
  }

  private async fillSAPSpecificQuestions(page: Page, data: ApplicationData): Promise<void> {
    const questions = await page.$$('.sap-question, .application-question');

    for (const question of questions) {
      try {
        const questionText = await question.$eval('.question-label, label', el => el.textContent?.trim());
        if (!questionText) continue;

        const answer = this.generateSAPAnswer(questionText, data);
        const inputType = await this.detectInputType(question);
        await this.fillQuestionAnswer(question, answer, inputType);
      } catch (error) {
        this.logger.warn(`Failed to process SAP question: ${error}`);
      }
    }
  }

  private generateSAPAnswer(questionText: string, data: ApplicationData): string {
    const lowerQuestion = questionText.toLowerCase();

    if (lowerQuestion.includes('sap experience')) {
      return data.sapExperience || 'I have experience with enterprise software and am eager to expand my knowledge of SAP solutions.';
    }

    if (lowerQuestion.includes('cloud')) {
      return 'Yes, I have experience with cloud platforms and am interested in SAP\'s cloud solutions.';
    }

    if (lowerQuestion.includes('erp')) {
      return 'Yes, I have experience with ERP systems and understand their importance in business operations.';
    }

    return 'Yes';
  }

  private async submitSAPForm(page: Page): Promise<void> {
    const submitButton = await page.$('button[type="submit"], input[value*="Submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
    } else {
      throw new Error('Submit button not found');
    }
  }
}

// Oracle Careers Portal Adapter
export class OraclePortalAdapter extends JobPortalAdapter {
  constructor(logger: Logger) {
    super('Oracle Careers', 'https://careers.oracle.com', logger);
  }

  async detectPortalType(page: Page): Promise<boolean> {
    try {
      const url = page.url();
      return url.includes('careers.oracle.com') || url.includes('oracle.com/careers');
    } catch {
      return false;
    }
  }

  async submitApplication(page: Page, data: ApplicationData): Promise<ApplicationResult> {
    try {
      await this.navigateToJob(page, data.jobUrl);
      await this.handleOracleLogin(page, data.credentials);
      await this.fillOracleApplication(page, data);
      await this.submitOracleForm(page);

      return {
        success: true,
        applicationId: await this.extractApplicationId(page),
        submittedAt: new Date(),
        confirmationNumber: await this.extractConfirmationNumber(page)
      };
    } catch (error) {
      this.logger.error(`Oracle application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        submittedAt: new Date()
      };
    }
  }

  private async handleOracleLogin(page: Page, credentials: any): Promise<void> {
    const loginLink = await page.$('a[href*="signin"], a[href*="login"]');
    if (!loginLink) return;

    await loginLink.click();
    await page.waitForSelector('#sso_username, #username', { timeout: 10000 });

    const usernameField = await page.$('#sso_username') || await page.$('#username');
    const passwordField = await page.$('#ssopassword') || await page.$('#password');

    if (usernameField && passwordField) {
      await usernameField.type(credentials.email);
      await passwordField.type(credentials.password);

      const signInButton = await page.$('input[value="Sign In"], button[type="submit"]');
      if (signInButton) {
        await signInButton.click();
        await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
      }
    }
  }

  private async fillOracleApplication(page: Page, data: ApplicationData): Promise<void> {
    // Oracle often uses Taleo-based application system
    await this.fillTaleoForm(page, data);
  }

  private async fillTaleoForm(page: Page, data: ApplicationData): Promise<void> {
    // Handle Taleo's dynamic form structure
    await page.waitForSelector('.requisitionDescriptionInterface, .taleoForm', { timeout: 15000 });

    // Click Apply button
    const applyButton = await page.$('a[title="Apply"], button:contains("Apply")');
    if (applyButton) {
      await applyButton.click();
      await page.waitForSelector('.taleoForm, .application-form', { timeout: 15000 });
    }

    // Fill personal information
    await this.fillTaleoPersonalInfo(page, data.personalInfo);

    // Upload documents
    await this.uploadTaleoDocuments(page, data);

    // Answer screening questions
    await this.fillTaleoScreeningQuestions(page, data);
  }

  private async fillTaleoPersonalInfo(page: Page, personalInfo: any): Promise<void> {
    const fields = [
      { selector: 'input[name*="firstName"]', value: personalInfo.firstName },
      { selector: 'input[name*="lastName"]', value: personalInfo.lastName },
      { selector: 'input[name*="email"]', value: personalInfo.email },
      { selector: 'input[name*="phone"]', value: personalInfo.phone }
    ];

    for (const field of fields) {
      await this.safeType(page, field.selector, field.value);
    }
  }

  private async uploadTaleoDocuments(page: Page, data: ApplicationData): Promise<void> {
    // Upload resume
    if (data.resumePath) {
      const resumeUpload = await page.$('input[type="file"][name*="resume"], input[type="file"][accept*="pdf"]');
      if (resumeUpload) {
        await resumeUpload.uploadFile(data.resumePath);
      }
    }

    // Upload cover letter if available
    if (data.coverLetterPath) {
      const coverLetterUpload = await page.$('input[type="file"][name*="coverLetter"]');
      if (coverLetterUpload) {
        await coverLetterUpload.uploadFile(data.coverLetterPath);
      }
    }
  }

  private async fillTaleoScreeningQuestions(page: Page, data: ApplicationData): Promise<void> {
    const questions = await page.$$('.screeningQuestion, .application-question');

    for (const question of questions) {
      try {
        const questionText = await question.$eval('.questionText, .question-label, label', el => el.textContent?.trim());
        if (!questionText) continue;

        const answer = this.generateOracleAnswer(questionText, data);
        const inputType = await this.detectInputType(question);
        await this.fillQuestionAnswer(question, answer, inputType);
      } catch (error) {
        this.logger.warn(`Failed to process Oracle question: ${error}`);
      }
    }
  }

  private generateOracleAnswer(questionText: string, data: ApplicationData): string {
    const lowerQuestion = questionText.toLowerCase();

    if (lowerQuestion.includes('oracle experience')) {
      return data.oracleExperience || 'I have experience with Oracle technologies and databases.';
    }

    if (lowerQuestion.includes('database') || lowerQuestion.includes('sql')) {
      return 'Yes, I have experience with database management and SQL.';
    }

    if (lowerQuestion.includes('security clearance')) {
      return data.securityClearance || 'No, but willing to obtain if required.';
    }

    if (lowerQuestion.includes('java') || lowerQuestion.includes('programming')) {
      return 'Yes, I have programming experience including Java and other technologies.';
    }

    return 'Yes';
  }

  private async submitOracleForm(page: Page): Promise<void> {
    const submitButton = await page.$('input[value*="Submit"], button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
    } else {
      throw new Error('Submit button not found');
    }
  }
}
