import { BaseAgent, AgentTask, AgentMessage, AgentCapability } from '../core/agent-orchestrator';
import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from '../../lib/db';
import nodemailer from 'nodemailer';
import { ImapFlow } from 'imapflow';

interface ApplicationMonitoringInput {
  userId: string;
  applicationIds: string[];
  monitoringSettings: {
    emailTracking: boolean;
    statusUpdates: boolean;
    followUpReminders: boolean;
    responseAnalysis: boolean;
  };
}

interface EmailAnalysisInput {
  userId: string;
  emailCredentials: {
    email: string;
    password: string;
    imapHost: string;
    imapPort: number;
  };
  lookbackDays: number;
}

interface PerformanceAnalysisInput {
  userId: string;
  timeRange: {
    startDate: Date;
    endDate: Date;
  };
  analysisType: 'individual' | 'aggregate' | 'comparative';
}

interface ApplicationStatus {
  id: string;
  applicationId: string;
  status: 'submitted' | 'viewed' | 'under_review' | 'interview_scheduled' | 'rejected' | 'offer_received';
  lastUpdated: Date;
  source: 'email' | 'portal' | 'manual' | 'automated';
  confidence: number;
  details?: {
    interviewDate?: Date;
    interviewType?: string;
    rejectionReason?: string;
    offerDetails?: any;
  };
}

interface EmailInsight {
  id: string;
  applicationId?: string;
  sender: string;
  subject: string;
  content: string;
  receivedAt: Date;
  classification: 'application_confirmation' | 'interview_invitation' | 'rejection' | 'offer' | 'follow_up' | 'other';
  sentiment: 'positive' | 'neutral' | 'negative';
  actionRequired: boolean;
  extractedData: {
    companyName?: string;
    position?: string;
    interviewDate?: Date;
    nextSteps?: string[];
  };
}

interface PerformanceMetrics {
  userId: string;
  timeRange: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    totalApplications: number;
    responseRate: number;
    interviewRate: number;
    offerRate: number;
    averageResponseTime: number;
    topPerformingResumes: string[];
    bestPerformingCompanies: string[];
    skillsInDemand: string[];
  };
  trends: {
    applicationTrend: number[];
    responseTrend: number[];
    successRate: number;
  };
  recommendations: string[];
}

interface FollowUpAction {
  id: string;
  applicationId: string;
  actionType: 'follow_up_email' | 'linkedin_message' | 'phone_call' | 'application_withdrawal';
  scheduledDate: Date;
  priority: 'high' | 'medium' | 'low';
  template?: string;
  status: 'pending' | 'completed' | 'skipped';
}

export class MonitoringAgent extends BaseAgent {
  private emailClient: nodemailer.Transporter;
  private imapClients: Map<string, ImapFlow> = new Map();
  private monitoringIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor(redis: Redis, logger: Logger) {
    const capabilities: AgentCapability[] = [
      {
        name: 'application_monitoring',
        description: 'Monitor job application status and responses',
        inputSchema: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            applicationIds: { type: 'array' },
            monitoringSettings: { type: 'object' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            statusUpdates: { type: 'array' },
            insights: { type: 'object' },
            actions: { type: 'array' }
          }
        },
        estimatedDuration: 300000, // 5 minutes
        resourceRequirements: {
          cpu: 0.5,
          memory: 512,
          network: true
        }
      },
      {
        name: 'email_analysis',
        description: 'Analyze emails for job application responses',
        inputSchema: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            emailCredentials: { type: 'object' },
            lookbackDays: { type: 'number' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            insights: { type: 'array' },
            statusUpdates: { type: 'array' },
            actionItems: { type: 'array' }
          }
        },
        estimatedDuration: 240000, // 4 minutes
        resourceRequirements: {
          cpu: 1,
          memory: 1024,
          network: true
        }
      },
      {
        name: 'performance_analysis',
        description: 'Analyze job search performance and generate insights',
        inputSchema: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            timeRange: { type: 'object' },
            analysisType: { type: 'string' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            metrics: { type: 'object' },
            trends: { type: 'object' },
            recommendations: { type: 'array' }
          }
        },
        estimatedDuration: 180000, // 3 minutes
        resourceRequirements: {
          cpu: 1.5,
          memory: 1536,
          network: false
        }
      }
    ];

    super('MonitoringAgent', capabilities, redis, logger);
    this.initializeEmailClient();
  }

  private initializeEmailClient(): void {
    this.emailClient = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    this.logger.info('Email client initialized');
  }

  protected async handleMessage(message: AgentMessage): Promise<void> {
    switch (message.type) {
      case 'start_monitoring':
        await this.handleStartMonitoring(message);
        break;
      case 'stop_monitoring':
        await this.handleStopMonitoring(message);
        break;
      case 'email_analysis_request':
        await this.handleEmailAnalysisRequest(message);
        break;
      case 'performance_analysis_request':
        await this.handlePerformanceAnalysisRequest(message);
        break;
      default:
        this.logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  protected async executeTask(task: AgentTask): Promise<any> {
    switch (task.type) {
      case 'application_monitoring':
        return await this.monitorApplications(task.data);
      case 'email_analysis':
        return await this.analyzeEmails(task.data);
      case 'performance_analysis':
        return await this.analyzePerformance(task.data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  private async monitorApplications(input: ApplicationMonitoringInput): Promise<any> {
    try {
      const statusUpdates: ApplicationStatus[] = [];
      const insights: any[] = [];
      const actions: FollowUpAction[] = [];

      // Fetch current application data
      const applications = await prisma.jobApplication.findMany({
        where: {
          id: { in: input.applicationIds },
          userId: input.userId
        },
        include: {
          job: true,
          resume: true
        }
      });

      // Monitor each application
      for (const application of applications) {
        // Check for status updates
        if (input.monitoringSettings.statusUpdates) {
          const statusUpdate = await this.checkApplicationStatus(application);
          if (statusUpdate) {
            statusUpdates.push(statusUpdate);
          }
        }

        // Generate follow-up actions
        if (input.monitoringSettings.followUpReminders) {
          const followUpAction = await this.generateFollowUpAction(application);
          if (followUpAction) {
            actions.push(followUpAction);
          }
        }

        // Analyze application performance
        const applicationInsight = await this.analyzeApplicationPerformance(application);
        insights.push(applicationInsight);
      }

      // Save status updates
      await this.saveStatusUpdates(statusUpdates);

      // Schedule follow-up actions
      await this.scheduleFollowUpActions(actions);

      return {
        statusUpdates,
        insights,
        actions,
        monitoringActive: true,
        nextCheckTime: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

    } catch (error) {
      this.logger.error(`Application monitoring failed: ${error}`);
      throw error;
    }
  }

  private async checkApplicationStatus(application: any): Promise<ApplicationStatus | null> {
    try {
      // Check various sources for status updates
      const portalStatus = await this.checkPortalStatus(application);
      const emailStatus = await this.checkEmailStatus(application);
      
      // Determine the most recent and reliable status
      const latestStatus = this.determineLatestStatus(portalStatus, emailStatus);
      
      if (latestStatus && latestStatus.status !== application.status) {
        return {
          id: uuidv4(),
          applicationId: application.id,
          status: latestStatus.status,
          lastUpdated: new Date(),
          source: latestStatus.source,
          confidence: latestStatus.confidence,
          details: latestStatus.details
        };
      }

      return null;

    } catch (error) {
      this.logger.error(`Status check failed for application ${application.id}: ${error}`);
      return null;
    }
  }

  private async checkPortalStatus(application: any): Promise<any> {
    // Mock implementation - would integrate with actual job portals
    return {
      status: 'under_review',
      source: 'portal',
      confidence: 0.8,
      lastUpdated: new Date()
    };
  }

  private async checkEmailStatus(application: any): Promise<any> {
    // Check emails for status updates related to this application
    const companyDomain = this.extractCompanyDomain(application.job.company);
    const recentEmails = await this.getRecentEmailsFromDomain(companyDomain);
    
    for (const email of recentEmails) {
      const classification = await this.classifyEmail(email, application);
      if (classification.isRelevant) {
        return {
          status: classification.status,
          source: 'email',
          confidence: classification.confidence,
          lastUpdated: email.receivedAt,
          details: classification.details
        };
      }
    }

    return null;
  }

  private extractCompanyDomain(companyName: string): string {
    // Simple domain extraction - would be more sophisticated in production
    return companyName.toLowerCase().replace(/\s+/g, '') + '.com';
  }

  private async getRecentEmailsFromDomain(domain: string): Promise<any[]> {
    // Mock implementation - would connect to actual email service
    return [];
  }

  private async classifyEmail(email: any, application: any): Promise<any> {
    // AI-powered email classification
    const content = email.content.toLowerCase();
    const subject = email.subject.toLowerCase();
    
    // Simple keyword-based classification
    if (subject.includes('interview') || content.includes('interview')) {
      return {
        isRelevant: true,
        status: 'interview_scheduled',
        confidence: 0.9,
        details: {
          interviewType: this.extractInterviewType(content),
          interviewDate: this.extractInterviewDate(content)
        }
      };
    }
    
    if (subject.includes('rejection') || content.includes('unfortunately') || content.includes('not moving forward')) {
      return {
        isRelevant: true,
        status: 'rejected',
        confidence: 0.85,
        details: {
          rejectionReason: this.extractRejectionReason(content)
        }
      };
    }
    
    if (subject.includes('offer') || content.includes('pleased to offer')) {
      return {
        isRelevant: true,
        status: 'offer_received',
        confidence: 0.95,
        details: {
          offerDetails: this.extractOfferDetails(content)
        }
      };
    }

    return {
      isRelevant: false,
      confidence: 0
    };
  }

  private extractInterviewType(content: string): string {
    if (content.includes('phone')) return 'phone';
    if (content.includes('video') || content.includes('zoom') || content.includes('teams')) return 'video';
    if (content.includes('onsite') || content.includes('office')) return 'onsite';
    return 'unknown';
  }

  private extractInterviewDate(content: string): Date | undefined {
    // Simple date extraction - would use more sophisticated NLP in production
    const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/;
    const match = content.match(datePattern);
    return match ? new Date(match[0]) : undefined;
  }

  private extractRejectionReason(content: string): string {
    // Extract rejection reason from email content
    if (content.includes('experience')) return 'insufficient_experience';
    if (content.includes('qualifications')) return 'qualifications_mismatch';
    if (content.includes('position filled')) return 'position_filled';
    return 'unspecified';
  }

  private extractOfferDetails(content: string): any {
    // Extract offer details from email content
    const salaryPattern = /\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;
    const salaryMatch = content.match(salaryPattern);
    
    return {
      salary: salaryMatch ? salaryMatch[0] : undefined,
      // Additional offer details would be extracted here
    };
  }

  private determineLatestStatus(portalStatus: any, emailStatus: any): any {
    if (!portalStatus && !emailStatus) return null;
    if (!portalStatus) return emailStatus;
    if (!emailStatus) return portalStatus;
    
    // Return the most recent status with highest confidence
    if (emailStatus.lastUpdated > portalStatus.lastUpdated) {
      return emailStatus.confidence > 0.7 ? emailStatus : portalStatus;
    } else {
      return portalStatus.confidence > 0.7 ? portalStatus : emailStatus;
    }
  }

  private async generateFollowUpAction(application: any): Promise<FollowUpAction | null> {
    const daysSinceApplication = Math.floor(
      (Date.now() - new Date(application.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    // Generate follow-up based on time elapsed and current status
    if (daysSinceApplication >= 7 && application.status === 'submitted') {
      return {
        id: uuidv4(),
        applicationId: application.id,
        actionType: 'follow_up_email',
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        priority: 'medium',
        template: 'follow_up_after_week',
        status: 'pending'
      };
    }

    if (daysSinceApplication >= 14 && application.status === 'under_review') {
      return {
        id: uuidv4(),
        applicationId: application.id,
        actionType: 'linkedin_message',
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
        priority: 'high',
        template: 'linkedin_follow_up',
        status: 'pending'
      };
    }

    return null;
  }

  private async analyzeApplicationPerformance(application: any): Promise<any> {
    const daysSinceApplication = Math.floor(
      (Date.now() - new Date(application.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      applicationId: application.id,
      company: application.job.company,
      position: application.job.title,
      status: application.status,
      daysSinceApplication,
      responseTime: this.calculateResponseTime(application),
      performanceScore: this.calculatePerformanceScore(application),
      insights: this.generateApplicationInsights(application)
    };
  }

  private calculateResponseTime(application: any): number {
    // Calculate time from application to first response
    if (application.status === 'submitted') return -1; // No response yet
    
    // Mock calculation - would use actual status change timestamps
    return Math.floor(Math.random() * 14) + 1; // 1-14 days
  }

  private calculatePerformanceScore(application: any): number {
    let score = 50; // Base score
    
    // Adjust based on status
    switch (application.status) {
      case 'viewed':
        score += 10;
        break;
      case 'under_review':
        score += 20;
        break;
      case 'interview_scheduled':
        score += 40;
        break;
      case 'offer_received':
        score += 50;
        break;
      case 'rejected':
        score -= 20;
        break;
    }

    // Adjust based on response time
    const responseTime = this.calculateResponseTime(application);
    if (responseTime > 0 && responseTime <= 3) {
      score += 15; // Quick response
    } else if (responseTime > 7) {
      score -= 10; // Slow response
    }

    return Math.max(0, Math.min(100, score));
  }

  private generateApplicationInsights(application: any): string[] {
    const insights: string[] = [];
    
    const daysSinceApplication = Math.floor(
      (Date.now() - new Date(application.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceApplication > 14 && application.status === 'submitted') {
      insights.push('No response after 2 weeks - consider following up');
    }

    if (application.status === 'interview_scheduled') {
      insights.push('Interview scheduled - prepare company-specific questions');
    }

    if (application.status === 'rejected') {
      insights.push('Application rejected - analyze feedback for future improvements');
    }

    return insights;
  }

  private async saveStatusUpdates(statusUpdates: ApplicationStatus[]): Promise<void> {
    for (const update of statusUpdates) {
      await prisma.jobApplication.update({
        where: { id: update.applicationId },
        data: { status: update.status }
      });

      // Save detailed status history
      await prisma.applicationStatusHistory.create({
        data: {
          id: update.id,
          applicationId: update.applicationId,
          status: update.status,
          source: update.source,
          confidence: update.confidence,
          details: update.details ? JSON.stringify(update.details) : null,
          createdAt: update.lastUpdated
        }
      });
    }

    this.logger.info(`Saved ${statusUpdates.length} status updates`);
  }

  private async scheduleFollowUpActions(actions: FollowUpAction[]): Promise<void> {
    for (const action of actions) {
      // Save to database
      await prisma.followUpAction.create({
        data: {
          id: action.id,
          applicationId: action.applicationId,
          actionType: action.actionType,
          scheduledDate: action.scheduledDate,
          priority: action.priority,
          template: action.template,
          status: action.status
        }
      });

      // Schedule execution
      const delay = action.scheduledDate.getTime() - Date.now();
      if (delay > 0) {
        setTimeout(() => {
          this.executeFollowUpAction(action);
        }, delay);
      }
    }

    this.logger.info(`Scheduled ${actions.length} follow-up actions`);
  }

  private async executeFollowUpAction(action: FollowUpAction): Promise<void> {
    try {
      switch (action.actionType) {
        case 'follow_up_email':
          await this.sendFollowUpEmail(action);
          break;
        case 'linkedin_message':
          await this.sendLinkedInMessage(action);
          break;
        case 'phone_call':
          await this.schedulePhoneCall(action);
          break;
        case 'application_withdrawal':
          await this.withdrawApplication(action);
          break;
      }

      // Mark action as completed
      await prisma.followUpAction.update({
        where: { id: action.id },
        data: { status: 'completed' }
      });

      this.logger.info(`Follow-up action completed: ${action.id}`);

    } catch (error) {
      this.logger.error(`Follow-up action failed: ${action.id} - ${error}`);
      
      await prisma.followUpAction.update({
        where: { id: action.id },
        data: { status: 'failed' }
      });
    }
  }

  private async sendFollowUpEmail(action: FollowUpAction): Promise<void> {
    // Get application details
    const application = await prisma.jobApplication.findUnique({
      where: { id: action.applicationId },
      include: { job: true, user: true }
    });

    if (!application) {
      throw new Error(`Application not found: ${action.applicationId}`);
    }

    // Generate email content based on template
    const emailContent = await this.generateEmailContent(action.template!, application);

    // Send email
    await this.emailClient.sendMail({
      from: application.user.email,
      to: this.getCompanyEmail(application.job.company),
      subject: emailContent.subject,
      html: emailContent.body
    });
  }

  private async generateEmailContent(template: string, application: any): Promise<any> {
    // Generate email content based on template and application data
    const templates = {
      follow_up_after_week: {
        subject: `Following up on ${application.job.title} application`,
        body: `
          <p>Dear Hiring Manager,</p>
          <p>I hope this email finds you well. I wanted to follow up on my application for the ${application.job.title} position that I submitted last week.</p>
          <p>I remain very interested in this opportunity and would welcome the chance to discuss how my skills and experience align with your team's needs.</p>
          <p>Thank you for your time and consideration.</p>
          <p>Best regards,<br>${application.user.name}</p>
        `
      }
    };

    return templates[template as keyof typeof templates] || {
      subject: 'Following up on job application',
      body: 'Thank you for considering my application.'
    };
  }

  private getCompanyEmail(companyName: string): string {
    // Simple email generation - would use actual company contact info
    return `careers@${companyName.toLowerCase().replace(/\s+/g, '')}.com`;
  }

  private async sendLinkedInMessage(action: FollowUpAction): Promise<void> {
    // LinkedIn message sending would be implemented here
    this.logger.info(`LinkedIn message scheduled for action: ${action.id}`);
  }

  private async schedulePhoneCall(action: FollowUpAction): Promise<void> {
    // Phone call scheduling would be implemented here
    this.logger.info(`Phone call scheduled for action: ${action.id}`);
  }

  private async withdrawApplication(action: FollowUpAction): Promise<void> {
    // Application withdrawal would be implemented here
    await prisma.jobApplication.update({
      where: { id: action.applicationId },
      data: { status: 'withdrawn' }
    });
  }

  private async analyzeEmails(input: EmailAnalysisInput): Promise<any> {
    try {
      // Connect to email account
      const imapClient = await this.connectToEmail(input.emailCredentials);
      
      // Fetch recent emails
      const emails = await this.fetchRecentEmails(imapClient, input.lookbackDays);
      
      // Analyze emails for job-related content
      const insights: EmailInsight[] = [];
      const statusUpdates: ApplicationStatus[] = [];
      const actionItems: string[] = [];

      for (const email of emails) {
        const insight = await this.analyzeEmailContent(email, input.userId);
        if (insight) {
          insights.push(insight);
          
          // Generate status update if applicable
          if (insight.applicationId) {
            const statusUpdate = this.emailInsightToStatusUpdate(insight);
            if (statusUpdate) {
              statusUpdates.push(statusUpdate);
            }
          }
          
          // Generate action items
          if (insight.actionRequired) {
            actionItems.push(`Follow up on ${insight.subject} from ${insight.sender}`);
          }
        }
      }

      // Disconnect from email
      await imapClient.logout();

      return {
        insights,
        statusUpdates,
        actionItems,
        summary: {
          totalEmails: emails.length,
          jobRelatedEmails: insights.length,
          positiveResponses: insights.filter(i => i.sentiment === 'positive').length,
          interviewInvitations: insights.filter(i => i.classification === 'interview_invitation').length,
          rejections: insights.filter(i => i.classification === 'rejection').length
        }
      };

    } catch (error) {
      this.logger.error(`Email analysis failed: ${error}`);
      throw error;
    }
  }

  private async connectToEmail(credentials: any): Promise<ImapFlow> {
    const client = new ImapFlow({
      host: credentials.imapHost,
      port: credentials.imapPort,
      secure: credentials.imapPort === 993,
      auth: {
        user: credentials.email,
        pass: credentials.password
      }
    });

    await client.connect();
    return client;
  }

  private async fetchRecentEmails(client: ImapFlow, lookbackDays: number): Promise<any[]> {
    await client.mailboxOpen('INBOX');
    
    const since = new Date();
    since.setDate(since.getDate() - lookbackDays);
    
    const messages = client.fetch({
      since
    }, {
      envelope: true,
      bodyStructure: true,
      source: true
    });

    const emails: any[] = [];
    for await (const message of messages) {
      emails.push({
        id: message.uid,
        sender: message.envelope.from?.[0]?.address || '',
        subject: message.envelope.subject || '',
        content: message.source?.toString() || '',
        receivedAt: message.envelope.date || new Date()
      });
    }

    return emails;
  }

  private async analyzeEmailContent(email: any, userId: string): Promise<EmailInsight | null> {
    // Check if email is job-related
    if (!this.isJobRelatedEmail(email)) {
      return null;
    }

    const classification = this.classifyEmailContent(email);
    const sentiment = this.analyzeSentiment(email.content);
    const extractedData = this.extractEmailData(email);

    return {
      id: uuidv4(),
      applicationId: await this.findRelatedApplication(email, userId),
      sender: email.sender,
      subject: email.subject,
      content: email.content,
      receivedAt: email.receivedAt,
      classification,
      sentiment,
      actionRequired: this.requiresAction(classification, sentiment),
      extractedData
    };
  }

  private isJobRelatedEmail(email: any): boolean {
    const jobKeywords = [
      'application', 'interview', 'position', 'role', 'opportunity',
      'hiring', 'recruitment', 'career', 'job', 'offer'
    ];

    const content = (email.subject + ' ' + email.content).toLowerCase();
    return jobKeywords.some(keyword => content.includes(keyword));
  }

  private classifyEmailContent(email: any): string {
    const content = email.content.toLowerCase();
    const subject = email.subject.toLowerCase();

    if (subject.includes('interview') || content.includes('interview')) {
      return 'interview_invitation';
    }
    if (subject.includes('offer') || content.includes('pleased to offer')) {
      return 'offer';
    }
    if (content.includes('unfortunately') || content.includes('not moving forward')) {
      return 'rejection';
    }
    if (subject.includes('application received') || content.includes('received your application')) {
      return 'application_confirmation';
    }
    if (subject.includes('follow up') || content.includes('following up')) {
      return 'follow_up';
    }

    return 'other';
  }

  private analyzeSentiment(content: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = ['pleased', 'excited', 'congratulations', 'offer', 'interview', 'impressed'];
    const negativeWords = ['unfortunately', 'regret', 'not', 'unable', 'declined', 'rejected'];

    const contentLower = content.toLowerCase();
    const positiveCount = positiveWords.filter(word => contentLower.includes(word)).length;
    const negativeCount = negativeWords.filter(word => contentLower.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private extractEmailData(email: any): any {
    return {
      companyName: this.extractCompanyName(email.sender),
      position: this.extractPosition(email.content),
      interviewDate: this.extractInterviewDate(email.content),
      nextSteps: this.extractNextSteps(email.content)
    };
  }

  private extractCompanyName(sender: string): string {
    // Extract company name from email domain
    const domain = sender.split('@')[1];
    if (domain) {
      return domain.split('.')[0];
    }
    return '';
  }

  private extractPosition(content: string): string {
    // Extract position title from email content
    const positionPattern = /(?:position|role|job)\s+(?:of|for|as)?\s*([A-Za-z\s]+)/i;
    const match = content.match(positionPattern);
    return match ? match[1].trim() : '';
  }

  private extractNextSteps(content: string): string[] {
    // Extract next steps from email content
    const steps: string[] = [];
    
    if (content.includes('please reply')) {
      steps.push('Reply to email');
    }
    if (content.includes('schedule') || content.includes('calendar')) {
      steps.push('Schedule interview');
    }
    if (content.includes('documents') || content.includes('paperwork')) {
      steps.push('Complete documentation');
    }

    return steps;
  }

  private async findRelatedApplication(email: any, userId: string): Promise<string | undefined> {
    // Find related job application based on email content
    const companyName = this.extractCompanyName(email.sender);
    const position = this.extractPosition(email.content);

    if (companyName || position) {
      const application = await prisma.jobApplication.findFirst({
        where: {
          userId,
          OR: [
            { job: { company: { contains: companyName, mode: 'insensitive' } } },
            { job: { title: { contains: position, mode: 'insensitive' } } }
          ]
        }
      });

      return application?.id;
    }

    return undefined;
  }

  private requiresAction(classification: string, sentiment: string): boolean {
    return classification === 'interview_invitation' || 
           (classification === 'offer' && sentiment === 'positive') ||
           classification === 'follow_up';
  }

  private emailInsightToStatusUpdate(insight: EmailInsight): ApplicationStatus | null {
    if (!insight.applicationId) return null;

    let status: any = null;
    
    switch (insight.classification) {
      case 'interview_invitation':
        status = 'interview_scheduled';
        break;
      case 'offer':
        status = 'offer_received';
        break;
      case 'rejection':
        status = 'rejected';
        break;
      case 'application_confirmation':
        status = 'viewed';
        break;
    }

    if (!status) return null;

    return {
      id: uuidv4(),
      applicationId: insight.applicationId,
      status,
      lastUpdated: insight.receivedAt,
      source: 'email',
      confidence: 0.9,
      details: insight.extractedData
    };
  }

  private async analyzePerformance(input: PerformanceAnalysisInput): Promise<PerformanceMetrics> {
    try {
      // Fetch applications within time range
      const applications = await prisma.jobApplication.findMany({
        where: {
          userId: input.userId,
          createdAt: {
            gte: input.timeRange.startDate,
            lte: input.timeRange.endDate
          }
        },
        include: {
          job: true,
          resume: true
        }
      });

      // Calculate metrics
      const totalApplications = applications.length;
      const responses = applications.filter(app => app.status !== 'submitted').length;
      const interviews = applications.filter(app => app.status === 'interview_scheduled').length;
      const offers = applications.filter(app => app.status === 'offer_received').length;

      const responseRate = totalApplications > 0 ? responses / totalApplications : 0;
      const interviewRate = totalApplications > 0 ? interviews / totalApplications : 0;
      const offerRate = totalApplications > 0 ? offers / totalApplications : 0;

      // Calculate average response time
      const responseTimes = applications
        .filter(app => app.status !== 'submitted')
        .map(app => this.calculateResponseTime(app))
        .filter(time => time > 0);
      
      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      // Analyze top performing elements
      const topPerformingResumes = this.analyzeTopPerformingResumes(applications);
      const bestPerformingCompanies = this.analyzeBestPerformingCompanies(applications);
      const skillsInDemand = this.analyzeSkillsInDemand(applications);

      // Generate trends
      const trends = this.generateTrends(applications, input.timeRange);

      // Generate recommendations
      const recommendations = this.generatePerformanceRecommendations({
        responseRate,
        interviewRate,
        offerRate,
        averageResponseTime,
        totalApplications
      });

      return {
        userId: input.userId,
        timeRange: input.timeRange,
        metrics: {
          totalApplications,
          responseRate,
          interviewRate,
          offerRate,
          averageResponseTime,
          topPerformingResumes,
          bestPerformingCompanies,
          skillsInDemand
        },
        trends,
        recommendations
      };

    } catch (error) {
      this.logger.error(`Performance analysis failed: ${error}`);
      throw error;
    }
  }

  private analyzeTopPerformingResumes(applications: any[]): string[] {
    const resumePerformance = new Map<string, { responses: number, total: number }>();

    applications.forEach(app => {
      const resumeId = app.resumeId;
      const current = resumePerformance.get(resumeId) || { responses: 0, total: 0 };
      
      current.total++;
      if (app.status !== 'submitted') {
        current.responses++;
      }
      
      resumePerformance.set(resumeId, current);
    });

    return Array.from(resumePerformance.entries())
      .map(([resumeId, perf]) => ({
        resumeId,
        rate: perf.total > 0 ? perf.responses / perf.total : 0
      }))
      .sort((a, b) => b.rate - a.rate)
      .slice(0, 3)
      .map(item => item.resumeId);
  }

  private analyzeBestPerformingCompanies(applications: any[]): string[] {
    const companyPerformance = new Map<string, { responses: number, total: number }>();

    applications.forEach(app => {
      const company = app.job.company;
      const current = companyPerformance.get(company) || { responses: 0, total: 0 };
      
      current.total++;
      if (app.status !== 'submitted') {
        current.responses++;
      }
      
      companyPerformance.set(company, current);
    });

    return Array.from(companyPerformance.entries())
      .map(([company, perf]) => ({
        company,
        rate: perf.total > 0 ? perf.responses / perf.total : 0
      }))
      .sort((a, b) => b.rate - a.rate)
      .slice(0, 5)
      .map(item => item.company);
  }

  private analyzeSkillsInDemand(applications: any[]): string[] {
    // Analyze job descriptions for common skills
    const skillCounts = new Map<string, number>();
    
    applications.forEach(app => {
      const description = app.job.description || '';
      const skills = this.extractSkillsFromDescription(description);
      
      skills.forEach(skill => {
        skillCounts.set(skill, (skillCounts.get(skill) || 0) + 1);
      });
    });

    return Array.from(skillCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([skill]) => skill);
  }

  private extractSkillsFromDescription(description: string): string[] {
    // Simple skill extraction - would use more sophisticated NLP in production
    const commonSkills = [
      'javascript', 'python', 'java', 'react', 'node.js', 'aws', 'docker',
      'kubernetes', 'sql', 'mongodb', 'git', 'agile', 'scrum'
    ];

    const descriptionLower = description.toLowerCase();
    return commonSkills.filter(skill => descriptionLower.includes(skill));
  }

  private generateTrends(applications: any[], timeRange: any): any {
    // Generate application and response trends over time
    const days = Math.ceil((timeRange.endDate.getTime() - timeRange.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const applicationTrend: number[] = new Array(days).fill(0);
    const responseTrend: number[] = new Array(days).fill(0);

    applications.forEach(app => {
      const dayIndex = Math.floor((new Date(app.createdAt).getTime() - timeRange.startDate.getTime()) / (1000 * 60 * 60 * 24));
      if (dayIndex >= 0 && dayIndex < days) {
        applicationTrend[dayIndex]++;
        if (app.status !== 'submitted') {
          responseTrend[dayIndex]++;
        }
      }
    });

    const totalResponses = applications.filter(app => app.status !== 'submitted').length;
    const successRate = applications.length > 0 ? totalResponses / applications.length : 0;

    return {
      applicationTrend,
      responseTrend,
      successRate
    };
  }

  private generatePerformanceRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.responseRate < 0.1) {
      recommendations.push('Response rate is low - consider improving resume and cover letter quality');
    }

    if (metrics.interviewRate < 0.05) {
      recommendations.push('Interview rate is low - focus on better job targeting and application customization');
    }

    if (metrics.averageResponseTime > 14) {
      recommendations.push('Companies are taking long to respond - consider following up after 1-2 weeks');
    }

    if (metrics.totalApplications < 10) {
      recommendations.push('Increase application volume to improve chances of success');
    }

    if (metrics.offerRate > 0.1) {
      recommendations.push('Excellent offer rate - continue current strategy');
    }

    return recommendations;
  }

  private async handleStartMonitoring(message: AgentMessage): Promise<void> {
    const { userId, interval = 3600000 } = message.payload; // Default 1 hour
    
    // Start monitoring interval for user
    const monitoringInterval = setInterval(async () => {
      await this.performPeriodicMonitoring(userId);
    }, interval);

    this.monitoringIntervals.set(userId, monitoringInterval);
    this.logger.info(`Started monitoring for user: ${userId}`);
  }

  private async handleStopMonitoring(message: AgentMessage): Promise<void> {
    const { userId } = message.payload;
    
    const interval = this.monitoringIntervals.get(userId);
    if (interval) {
      clearInterval(interval);
      this.monitoringIntervals.delete(userId);
      this.logger.info(`Stopped monitoring for user: ${userId}`);
    }
  }

  private async performPeriodicMonitoring(userId: string): Promise<void> {
    try {
      // Get active applications for user
      const applications = await prisma.jobApplication.findMany({
        where: {
          userId,
          status: { in: ['submitted', 'viewed', 'under_review'] }
        }
      });

      if (applications.length > 0) {
        // Perform monitoring
        await this.monitorApplications({
          userId,
          applicationIds: applications.map(app => app.id),
          monitoringSettings: {
            emailTracking: true,
            statusUpdates: true,
            followUpReminders: true,
            responseAnalysis: true
          }
        });
      }

    } catch (error) {
      this.logger.error(`Periodic monitoring failed for user ${userId}: ${error}`);
    }
  }

  private async handleEmailAnalysisRequest(message: AgentMessage): Promise<void> {
    this.logger.info('Email analysis request received', message.payload);
  }

  private async handlePerformanceAnalysisRequest(message: AgentMessage): Promise<void> {
    this.logger.info('Performance analysis request received', message.payload);
  }

  public async shutdown(): Promise<void> {
    // Clear all monitoring intervals
    this.monitoringIntervals.forEach(interval => clearInterval(interval));
    this.monitoringIntervals.clear();

    // Close all IMAP connections
    for (const [userId, client] of this.imapClients) {
      try {
        await client.logout();
      } catch (error) {
        this.logger.error(`Error closing IMAP connection for ${userId}: ${error}`);
      }
    }
    this.imapClients.clear();

    this.logger.info('Monitoring agent shutdown complete');
  }
}
