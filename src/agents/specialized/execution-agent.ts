import { BaseAgent, AgentTask, AgentMessage, AgentCapability } from '../core/agent-orchestrator';
import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { JobPortalAdapter } from '../adapters/job-portal-adapter';

interface ApplicationSubmissionData {
  userId: string;
  jobId: string;
  jobUrl: string;
  resumeData: any;
  coverLetterData: any;
  personalInfo: any;
  applicationAnswers: { [key: string]: string };
  portalType: string;
}

interface SubmissionResult {
  success: boolean;
  applicationId?: string;
  confirmationNumber?: string;
  submissionUrl?: string;
  error?: string;
  screenshots?: string[];
  nextSteps?: string[];
}

interface BrowserSession {
  browser: Browser;
  page: Page;
  sessionId: string;
  userId: string;
  createdAt: Date;
  lastActivity: Date;
}

export class ExecutionAgent extends BaseAgent {
  private browserSessions: Map<string, BrowserSession> = new Map();
  private portalAdapters: Map<string, JobPortalAdapter> = new Map();
  private maxConcurrentSessions: number = 5;
  private sessionTimeout: number = 1800000; // 30 minutes

  constructor(redis: Redis, logger: Logger) {
    const capabilities: AgentCapability[] = [
      {
        name: 'job_application_submission',
        description: 'Submit job applications through various job portals',
        inputSchema: {
          type: 'object',
          properties: {
            applicationData: { type: 'object' },
            portalType: { type: 'string' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            result: { type: 'object' },
            screenshots: { type: 'array' }
          }
        },
        estimatedDuration: 300000, // 5 minutes
        resourceRequirements: {
          cpu: 2,
          memory: 2048,
          network: true
        }
      },
      {
        name: 'form_automation',
        description: 'Automate complex form filling and submission',
        inputSchema: {
          type: 'object',
          properties: {
            formData: { type: 'object' },
            formUrl: { type: 'string' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' }
          }
        },
        estimatedDuration: 180000, // 3 minutes
        resourceRequirements: {
          cpu: 1.5,
          memory: 1536,
          network: true
        }
      },
      {
        name: 'batch_application',
        description: 'Submit multiple applications in batch',
        inputSchema: {
          type: 'object',
          properties: {
            applications: { type: 'array' },
            batchSize: { type: 'number' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            results: { type: 'array' },
            summary: { type: 'object' }
          }
        },
        estimatedDuration: 1800000, // 30 minutes
        resourceRequirements: {
          cpu: 3,
          memory: 4096,
          network: true
        }
      }
    ];

    super('ExecutionAgent', capabilities, redis, logger);
    this.initializePortalAdapters();
    this.startSessionCleanup();
  }

  private initializePortalAdapters(): void {
    // Initialize adapters for different job portals
    // This would be expanded with actual portal implementations
    this.logger.info('Initializing job portal adapters');
  }

  private startSessionCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 300000); // Check every 5 minutes
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.browserSessions) {
      if (now - session.lastActivity.getTime() > this.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      await this.closeBrowserSession(sessionId);
    }

    if (expiredSessions.length > 0) {
      this.logger.info(`Cleaned up ${expiredSessions.length} expired browser sessions`);
    }
  }

  protected async handleMessage(message: AgentMessage): Promise<void> {
    switch (message.type) {
      case 'submit_application':
        await this.handleApplicationSubmission(message);
        break;
      case 'batch_submit':
        await this.handleBatchSubmission(message);
        break;
      case 'session_cleanup':
        await this.handleSessionCleanup(message);
        break;
      default:
        this.logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  protected async executeTask(task: AgentTask): Promise<any> {
    switch (task.type) {
      case 'job_application_submission':
        return await this.submitJobApplication(task.data);
      case 'form_automation':
        return await this.automateForm(task.data);
      case 'batch_application':
        return await this.submitBatchApplications(task.data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  private async submitJobApplication(data: ApplicationSubmissionData): Promise<SubmissionResult> {
    const sessionId = await this.createBrowserSession(data.userId);
    
    try {
      const session = this.browserSessions.get(sessionId);
      if (!session) {
        throw new Error('Failed to create browser session');
      }

      const adapter = this.getPortalAdapter(data.portalType);
      if (!adapter) {
        throw new Error(`No adapter found for portal type: ${data.portalType}`);
      }

      // Navigate to job posting
      await session.page.goto(data.jobUrl, { waitUntil: 'networkidle0' });
      
      // Take screenshot for debugging
      const screenshots: string[] = [];
      screenshots.push(await this.takeScreenshot(session.page, 'initial_page'));

      // Use adapter to handle portal-specific logic
      const result = await adapter.submitApplication(session.page, data);
      
      // Take final screenshot
      screenshots.push(await this.takeScreenshot(session.page, 'final_result'));

      // Update session activity
      session.lastActivity = new Date();

      return {
        ...result,
        screenshots
      };

    } catch (error) {
      this.logger.error(`Application submission failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        screenshots: []
      };
    } finally {
      // Keep session alive for potential follow-up actions
      // It will be cleaned up by the timeout mechanism
    }
  }

  private async automateForm(data: any): Promise<any> {
    const { formData, formUrl, userId } = data;
    const sessionId = await this.createBrowserSession(userId);
    
    try {
      const session = this.browserSessions.get(sessionId);
      if (!session) {
        throw new Error('Failed to create browser session');
      }

      await session.page.goto(formUrl, { waitUntil: 'networkidle0' });
      
      // Generic form automation logic
      const result = await this.fillForm(session.page, formData);
      
      session.lastActivity = new Date();
      
      return {
        success: true,
        data: result
      };

    } catch (error) {
      this.logger.error(`Form automation failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async submitBatchApplications(data: any): Promise<any> {
    const { applications, batchSize, userId } = data;
    const results: SubmissionResult[] = [];
    const batchResults = {
      total: applications.length,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Process applications in batches to avoid overwhelming the system
    for (let i = 0; i < applications.length; i += batchSize) {
      const batch = applications.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (appData: ApplicationSubmissionData) => {
        try {
          const result = await this.submitJobApplication(appData);
          if (result.success) {
            batchResults.successful++;
          } else {
            batchResults.failed++;
            if (result.error) {
              batchResults.errors.push(result.error);
            }
          }
          return result;
        } catch (error) {
          batchResults.failed++;
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          batchResults.errors.push(errorMsg);
          return {
            success: false,
            error: errorMsg
          } as SubmissionResult;
        }
      });

      const batchResults_temp = await Promise.all(batchPromises);
      results.push(...batchResults_temp);

      // Add delay between batches to be respectful to job portals
      if (i + batchSize < applications.length) {
        await this.delay(5000); // 5 second delay between batches
      }
    }

    return {
      results,
      summary: batchResults
    };
  }

  private async createBrowserSession(userId: string): Promise<string> {
    // Check if we're at the session limit
    if (this.browserSessions.size >= this.maxConcurrentSessions) {
      // Close the oldest session
      const oldestSession = Array.from(this.browserSessions.values())
        .sort((a, b) => a.lastActivity.getTime() - b.lastActivity.getTime())[0];
      await this.closeBrowserSession(oldestSession.sessionId);
    }

    const sessionId = uuidv4();
    
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      ]
    });

    const page = await browser.newPage();
    
    // Set viewport and other page configurations
    await page.setViewport({ width: 1920, height: 1080 });
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9'
    });

    const session: BrowserSession = {
      browser,
      page,
      sessionId,
      userId,
      createdAt: new Date(),
      lastActivity: new Date()
    };

    this.browserSessions.set(sessionId, session);
    
    this.logger.info(`Created browser session ${sessionId} for user ${userId}`);
    
    return sessionId;
  }

  private async closeBrowserSession(sessionId: string): Promise<void> {
    const session = this.browserSessions.get(sessionId);
    if (session) {
      try {
        await session.browser.close();
        this.browserSessions.delete(sessionId);
        this.logger.info(`Closed browser session ${sessionId}`);
      } catch (error) {
        this.logger.error(`Error closing browser session ${sessionId}: ${error}`);
      }
    }
  }

  private getPortalAdapter(portalType: string): JobPortalAdapter | null {
    return this.portalAdapters.get(portalType) || null;
  }

  private async takeScreenshot(page: Page, name: string): Promise<string> {
    try {
      const screenshot = await page.screenshot({
        fullPage: true,
        type: 'png'
      });
      
      // In a real implementation, you would upload this to cloud storage
      // and return the URL. For now, we'll return a placeholder
      const filename = `screenshot_${name}_${Date.now()}.png`;
      
      // Store screenshot data (in production, upload to S3/GCS/etc.)
      // await uploadToStorage(screenshot, filename);
      
      return filename;
    } catch (error) {
      this.logger.error(`Failed to take screenshot: ${error}`);
      return '';
    }
  }

  private async fillForm(page: Page, formData: { [key: string]: string }): Promise<any> {
    const results: any = {};
    
    for (const [fieldName, value] of Object.entries(formData)) {
      try {
        // Try multiple selectors for the field
        const selectors = [
          `input[name="${fieldName}"]`,
          `input[id="${fieldName}"]`,
          `textarea[name="${fieldName}"]`,
          `textarea[id="${fieldName}"]`,
          `select[name="${fieldName}"]`,
          `select[id="${fieldName}"]`
        ];

        let fieldFound = false;
        for (const selector of selectors) {
          const element = await page.$(selector);
          if (element) {
            const tagName = await element.evaluate(el => el.tagName.toLowerCase());
            
            if (tagName === 'select') {
              await element.select(value);
            } else {
              await element.clear();
              await element.type(value);
            }
            
            fieldFound = true;
            results[fieldName] = 'filled';
            break;
          }
        }

        if (!fieldFound) {
          results[fieldName] = 'not_found';
          this.logger.warn(`Field not found: ${fieldName}`);
        }

      } catch (error) {
        results[fieldName] = 'error';
        this.logger.error(`Error filling field ${fieldName}: ${error}`);
      }
    }

    return results;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async handleApplicationSubmission(message: AgentMessage): Promise<void> {
    // Handle direct application submission requests
    this.logger.info('Application submission request received', message.payload);
  }

  private async handleBatchSubmission(message: AgentMessage): Promise<void> {
    // Handle batch submission requests
    this.logger.info('Batch submission request received', message.payload);
  }

  private async handleSessionCleanup(message: AgentMessage): Promise<void> {
    // Handle session cleanup requests
    await this.cleanupExpiredSessions();
  }

  public async shutdown(): Promise<void> {
    // Close all browser sessions
    const sessionIds = Array.from(this.browserSessions.keys());
    await Promise.all(sessionIds.map(id => this.closeBrowserSession(id)));
    
    this.logger.info('Execution agent shutdown complete');
  }
}
