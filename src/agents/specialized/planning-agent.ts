import { BaseAgent, AgentTask, AgentMessage, AgentCapability } from '../core/agent-orchestrator';
import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';

interface JobSearchGoal {
  userId: string;
  targetRoles: string[];
  targetCompanies: string[];
  salaryRange: { min: number; max: number };
  location: string[];
  remote: boolean;
  timeline: Date;
  preferences: {
    industryPreferences: string[];
    companySizePreference: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
    workLifeBalance: number; // 1-10 scale
    careerGrowth: number; // 1-10 scale
  };
}

interface TaskPlan {
  id: string;
  name: string;
  description: string;
  type: string;
  priority: number;
  estimatedDuration: number;
  dependencies: string[];
  data: any;
  deadline?: Date;
}

interface JobApplicationPipeline {
  goalId: string;
  userId: string;
  phases: {
    research: TaskPlan[];
    preparation: TaskPlan[];
    execution: TaskPlan[];
    monitoring: TaskPlan[];
  };
  totalEstimatedTime: number;
  criticalPath: string[];
}

export class PlanningAgent extends BaseAgent {
  private activeGoals: Map<string, JobSearchGoal> = new Map();
  private activePipelines: Map<string, JobApplicationPipeline> = new Map();

  constructor(redis: Redis, logger: Logger) {
    const capabilities: AgentCapability[] = [
      {
        name: 'goal_decomposition',
        description: 'Break down job search goals into actionable tasks',
        inputSchema: {
          type: 'object',
          properties: {
            goal: { type: 'object' },
            constraints: { type: 'object' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            pipeline: { type: 'object' },
            tasks: { type: 'array' }
          }
        },
        estimatedDuration: 30000, // 30 seconds
        resourceRequirements: {
          cpu: 0.5,
          memory: 512,
          network: false
        }
      },
      {
        name: 'task_prioritization',
        description: 'Prioritize tasks based on deadlines and dependencies',
        inputSchema: {
          type: 'object',
          properties: {
            tasks: { type: 'array' },
            constraints: { type: 'object' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            prioritizedTasks: { type: 'array' },
            schedule: { type: 'object' }
          }
        },
        estimatedDuration: 15000, // 15 seconds
        resourceRequirements: {
          cpu: 0.3,
          memory: 256,
          network: false
        }
      },
      {
        name: 'pipeline_optimization',
        description: 'Optimize task execution pipeline for efficiency',
        inputSchema: {
          type: 'object',
          properties: {
            pipeline: { type: 'object' },
            resources: { type: 'object' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            optimizedPipeline: { type: 'object' },
            improvements: { type: 'array' }
          }
        },
        estimatedDuration: 45000, // 45 seconds
        resourceRequirements: {
          cpu: 0.8,
          memory: 1024,
          network: false
        }
      }
    ];

    super('PlanningAgent', capabilities, redis, logger);
  }

  protected async handleMessage(message: AgentMessage): Promise<void> {
    switch (message.type) {
      case 'new_job_search_goal':
        await this.handleNewJobSearchGoal(message);
        break;
      case 'update_goal':
        await this.handleUpdateGoal(message);
        break;
      case 'task_status_update':
        await this.handleTaskStatusUpdate(message);
        break;
      case 'pipeline_adjustment_request':
        await this.handlePipelineAdjustment(message);
        break;
      default:
        this.logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  protected async executeTask(task: AgentTask): Promise<any> {
    switch (task.type) {
      case 'goal_decomposition':
        return await this.decomposeGoal(task.data);
      case 'task_prioritization':
        return await this.prioritizeTasks(task.data);
      case 'pipeline_optimization':
        return await this.optimizePipeline(task.data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  private async handleNewJobSearchGoal(message: AgentMessage): Promise<void> {
    const goal: JobSearchGoal = message.payload.goal;
    this.activeGoals.set(goal.userId, goal);
    
    // Create initial pipeline
    const pipeline = await this.createJobApplicationPipeline(goal);
    this.activePipelines.set(goal.userId, pipeline);
    
    // Submit tasks to orchestrator
    await this.submitPipelineTasks(pipeline);
    
    this.logger.info(`New job search goal created for user ${goal.userId}`);
  }

  private async createJobApplicationPipeline(goal: JobSearchGoal): Promise<JobApplicationPipeline> {
    const pipeline: JobApplicationPipeline = {
      goalId: uuidv4(),
      userId: goal.userId,
      phases: {
        research: [],
        preparation: [],
        execution: [],
        monitoring: []
      },
      totalEstimatedTime: 0,
      criticalPath: []
    };

    // Research Phase
    pipeline.phases.research = [
      {
        id: uuidv4(),
        name: 'Market Analysis',
        description: 'Analyze job market trends for target roles',
        type: 'market_analysis',
        priority: 10,
        estimatedDuration: 300000, // 5 minutes
        dependencies: [],
        data: {
          targetRoles: goal.targetRoles,
          location: goal.location,
          salaryRange: goal.salaryRange
        }
      },
      {
        id: uuidv4(),
        name: 'Company Research',
        description: 'Research target companies and their requirements',
        type: 'company_research',
        priority: 9,
        estimatedDuration: 600000, // 10 minutes
        dependencies: [],
        data: {
          targetCompanies: goal.targetCompanies,
          preferences: goal.preferences
        }
      },
      {
        id: uuidv4(),
        name: 'Job Opportunity Discovery',
        description: 'Find and analyze relevant job postings',
        type: 'job_discovery',
        priority: 8,
        estimatedDuration: 900000, // 15 minutes
        dependencies: [],
        data: {
          searchCriteria: {
            roles: goal.targetRoles,
            companies: goal.targetCompanies,
            location: goal.location,
            remote: goal.remote
          }
        }
      }
    ];

    // Preparation Phase
    const researchTaskIds = pipeline.phases.research.map(task => task.id);
    pipeline.phases.preparation = [
      {
        id: uuidv4(),
        name: 'Resume Optimization',
        description: 'Optimize resume for discovered opportunities',
        type: 'resume_optimization',
        priority: 7,
        estimatedDuration: 1200000, // 20 minutes
        dependencies: researchTaskIds,
        data: {
          userId: goal.userId,
          targetRoles: goal.targetRoles
        }
      },
      {
        id: uuidv4(),
        name: 'Cover Letter Templates',
        description: 'Create personalized cover letter templates',
        type: 'cover_letter_generation',
        priority: 6,
        estimatedDuration: 600000, // 10 minutes
        dependencies: researchTaskIds,
        data: {
          userId: goal.userId,
          targetCompanies: goal.targetCompanies
        }
      },
      {
        id: uuidv4(),
        name: 'Application Strategy',
        description: 'Develop application timing and approach strategy',
        type: 'application_strategy',
        priority: 5,
        estimatedDuration: 300000, // 5 minutes
        dependencies: researchTaskIds,
        data: {
          timeline: goal.timeline,
          preferences: goal.preferences
        }
      }
    ];

    // Execution Phase
    const preparationTaskIds = pipeline.phases.preparation.map(task => task.id);
    pipeline.phases.execution = [
      {
        id: uuidv4(),
        name: 'Batch Application Submission',
        description: 'Submit applications to identified opportunities',
        type: 'batch_application',
        priority: 4,
        estimatedDuration: 3600000, // 60 minutes
        dependencies: preparationTaskIds,
        data: {
          userId: goal.userId,
          batchSize: 10
        }
      },
      {
        id: uuidv4(),
        name: 'Follow-up Scheduling',
        description: 'Schedule follow-up actions for submitted applications',
        type: 'follow_up_scheduling',
        priority: 3,
        estimatedDuration: 300000, // 5 minutes
        dependencies: preparationTaskIds,
        data: {
          userId: goal.userId,
          followUpStrategy: 'standard'
        }
      }
    ];

    // Monitoring Phase
    const executionTaskIds = pipeline.phases.execution.map(task => task.id);
    pipeline.phases.monitoring = [
      {
        id: uuidv4(),
        name: 'Application Tracking',
        description: 'Monitor application status and responses',
        type: 'application_monitoring',
        priority: 2,
        estimatedDuration: 0, // Continuous
        dependencies: executionTaskIds,
        data: {
          userId: goal.userId,
          monitoringInterval: 3600000 // Check every hour
        }
      },
      {
        id: uuidv4(),
        name: 'Performance Analytics',
        description: 'Analyze application success rates and optimize',
        type: 'performance_analytics',
        priority: 1,
        estimatedDuration: 600000, // 10 minutes
        dependencies: executionTaskIds,
        data: {
          userId: goal.userId,
          analysisType: 'comprehensive'
        }
      }
    ];

    // Calculate total estimated time and critical path
    const allTasks = [
      ...pipeline.phases.research,
      ...pipeline.phases.preparation,
      ...pipeline.phases.execution,
      ...pipeline.phases.monitoring
    ];
    
    pipeline.totalEstimatedTime = allTasks.reduce((total, task) => total + task.estimatedDuration, 0);
    pipeline.criticalPath = this.calculateCriticalPath(allTasks);

    return pipeline;
  }

  private calculateCriticalPath(tasks: TaskPlan[]): string[] {
    // Simplified critical path calculation
    // In a real implementation, this would use proper CPM algorithm
    const taskMap = new Map(tasks.map(task => [task.id, task]));
    const visited = new Set<string>();
    const criticalPath: string[] = [];

    const findLongestPath = (taskId: string, currentPath: string[], currentDuration: number): { path: string[], duration: number } => {
      if (visited.has(taskId)) {
        return { path: currentPath, duration: currentDuration };
      }

      visited.add(taskId);
      const task = taskMap.get(taskId);
      if (!task) {
        return { path: currentPath, duration: currentDuration };
      }

      const newPath = [...currentPath, taskId];
      const newDuration = currentDuration + task.estimatedDuration;

      if (task.dependencies.length === 0) {
        return { path: newPath, duration: newDuration };
      }

      let longestPath = { path: newPath, duration: newDuration };
      for (const depId of task.dependencies) {
        const depPath = findLongestPath(depId, newPath, newDuration);
        if (depPath.duration > longestPath.duration) {
          longestPath = depPath;
        }
      }

      return longestPath;
    };

    // Find the task with no dependents (end task)
    const endTasks = tasks.filter(task => 
      !tasks.some(otherTask => otherTask.dependencies.includes(task.id))
    );

    let longestOverallPath = { path: [], duration: 0 };
    for (const endTask of endTasks) {
      visited.clear();
      const path = findLongestPath(endTask.id, [], 0);
      if (path.duration > longestOverallPath.duration) {
        longestOverallPath = path;
      }
    }

    return longestOverallPath.path;
  }

  private async submitPipelineTasks(pipeline: JobApplicationPipeline): Promise<void> {
    const allTasks = [
      ...pipeline.phases.research,
      ...pipeline.phases.preparation,
      ...pipeline.phases.execution,
      ...pipeline.phases.monitoring
    ];

    for (const taskPlan of allTasks) {
      const agentTask: AgentTask = {
        id: taskPlan.id,
        type: taskPlan.type,
        userId: pipeline.userId,
        status: 'pending',
        priority: taskPlan.priority,
        data: taskPlan.data,
        dependencies: taskPlan.dependencies,
        createdAt: new Date(),
        updatedAt: new Date(),
        deadline: taskPlan.deadline
      };

      await this.sendMessage({
        id: uuidv4(),
        type: 'submit_task',
        payload: { task: agentTask },
        sender: this.getId(),
        recipient: 'orchestrator',
        timestamp: new Date(),
        priority: 'medium'
      });
    }

    this.logger.info(`Submitted ${allTasks.length} tasks for pipeline ${pipeline.goalId}`);
  }

  private async decomposeGoal(data: any): Promise<any> {
    const { goal, constraints } = data;
    
    // AI-powered goal decomposition logic
    const pipeline = await this.createJobApplicationPipeline(goal);
    
    return {
      pipeline,
      tasks: [
        ...pipeline.phases.research,
        ...pipeline.phases.preparation,
        ...pipeline.phases.execution,
        ...pipeline.phases.monitoring
      ],
      estimatedCompletion: new Date(Date.now() + pipeline.totalEstimatedTime),
      criticalPath: pipeline.criticalPath
    };
  }

  private async prioritizeTasks(data: any): Promise<any> {
    const { tasks, constraints } = data;
    
    // Implement task prioritization algorithm
    const prioritizedTasks = tasks.sort((a: TaskPlan, b: TaskPlan) => {
      // Priority factors: deadline urgency, dependencies, resource availability
      const aUrgency = a.deadline ? (a.deadline.getTime() - Date.now()) : Infinity;
      const bUrgency = b.deadline ? (b.deadline.getTime() - Date.now()) : Infinity;
      
      if (aUrgency !== bUrgency) {
        return aUrgency - bUrgency;
      }
      
      return b.priority - a.priority;
    });
    
    return {
      prioritizedTasks,
      schedule: this.generateSchedule(prioritizedTasks),
      recommendations: this.generatePriorityRecommendations(prioritizedTasks)
    };
  }

  private async optimizePipeline(data: any): Promise<any> {
    const { pipeline, resources } = data;
    
    // Implement pipeline optimization logic
    const optimizations = [
      'Parallel execution of independent tasks',
      'Resource allocation optimization',
      'Bottleneck identification and resolution',
      'Critical path optimization'
    ];
    
    return {
      optimizedPipeline: pipeline,
      improvements: optimizations,
      estimatedTimeReduction: 0.15, // 15% improvement
      resourceEfficiency: 0.20 // 20% better resource utilization
    };
  }

  private generateSchedule(tasks: TaskPlan[]): any {
    // Generate execution schedule
    return {
      startTime: new Date(),
      phases: tasks.reduce((phases, task) => {
        const phase = this.getTaskPhase(task.type);
        if (!phases[phase]) {
          phases[phase] = [];
        }
        phases[phase].push(task);
        return phases;
      }, {} as any)
    };
  }

  private generatePriorityRecommendations(tasks: TaskPlan[]): string[] {
    return [
      'Focus on research tasks first to gather market intelligence',
      'Prepare application materials in parallel where possible',
      'Batch similar application submissions for efficiency',
      'Set up monitoring early to track application progress'
    ];
  }

  private getTaskPhase(taskType: string): string {
    const phaseMap: { [key: string]: string } = {
      'market_analysis': 'research',
      'company_research': 'research',
      'job_discovery': 'research',
      'resume_optimization': 'preparation',
      'cover_letter_generation': 'preparation',
      'application_strategy': 'preparation',
      'batch_application': 'execution',
      'follow_up_scheduling': 'execution',
      'application_monitoring': 'monitoring',
      'performance_analytics': 'monitoring'
    };
    
    return phaseMap[taskType] || 'unknown';
  }

  private async handleUpdateGoal(message: AgentMessage): Promise<void> {
    // Handle goal updates
    this.logger.info('Goal update received', message.payload);
  }

  private async handleTaskStatusUpdate(message: AgentMessage): Promise<void> {
    // Handle task status updates and adjust pipeline if needed
    this.logger.info('Task status update received', message.payload);
  }

  private async handlePipelineAdjustment(message: AgentMessage): Promise<void> {
    // Handle pipeline adjustment requests
    this.logger.info('Pipeline adjustment request received', message.payload);
  }

  public getActivePipelines(): Map<string, JobApplicationPipeline> {
    return this.activePipelines;
  }

  public getActiveGoals(): Map<string, JobSearchGoal> {
    return this.activeGoals;
  }
}
