import { BaseA<PERSON>, AgentTask, AgentMessage, AgentCapability } from '../core/agent-orchestrator';
import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { prisma } from '../../lib/db';

interface JobMarketAnalysisInput {
  targetRoles: string[];
  location: string[];
  salaryRange?: { min: number; max: number };
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industries?: string[];
}

interface CompanyResearchInput {
  companyNames: string[];
  researchDepth: 'basic' | 'detailed' | 'comprehensive';
  focusAreas: string[];
}

interface JobDiscoveryInput {
  searchCriteria: {
    roles: string[];
    companies?: string[];
    location: string[];
    remote: boolean;
    salaryMin?: number;
  };
  maxResults: number;
  platforms: string[];
}

interface MarketTrend {
  skill: string;
  demand: 'high' | 'medium' | 'low';
  growth: number; // percentage
  averageSalary: number;
  jobCount: number;
  locations: string[];
}

interface CompanyInsight {
  name: string;
  industry: string;
  size: string;
  culture: {
    workLifeBalance: number;
    diversity: number;
    innovation: number;
    compensation: number;
  };
  hiringTrends: {
    activeRoles: number;
    averageTimeToHire: number;
    preferredSkills: string[];
  };
  applicationTips: string[];
  recentNews: string[];
}

interface JobOpportunity {
  id: string;
  title: string;
  company: string;
  location: string;
  remote: boolean;
  salary?: { min: number; max: number };
  description: string;
  requirements: string[];
  benefits: string[];
  applicationUrl: string;
  postedDate: Date;
  matchScore: number;
  portalType: string;
}

export class ResearchAgent extends BaseAgent {
  private browserPool: Browser[] = [];
  private maxBrowsers: number = 3;
  private jobBoardUrls: Map<string, string> = new Map([
    ['linkedin', 'https://www.linkedin.com/jobs/search'],
    ['indeed', 'https://www.indeed.com/jobs'],
    ['glassdoor', 'https://www.glassdoor.com/Job/jobs.htm'],
    ['dice', 'https://www.dice.com/jobs'],
    ['stackoverflow', 'https://stackoverflow.com/jobs'],
    ['angellist', 'https://angel.co/jobs'],
    ['remote', 'https://remote.co/remote-jobs'],
    ['weworkremotely', 'https://weworkremotely.com']
  ]);

  constructor(redis: Redis, logger: Logger) {
    const capabilities: AgentCapability[] = [
      {
        name: 'market_analysis',
        description: 'Analyze job market trends and salary data',
        inputSchema: {
          type: 'object',
          properties: {
            targetRoles: { type: 'array' },
            location: { type: 'array' },
            experienceLevel: { type: 'string' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            trends: { type: 'array' },
            insights: { type: 'object' },
            recommendations: { type: 'array' }
          }
        },
        estimatedDuration: 300000, // 5 minutes
        resourceRequirements: {
          cpu: 1.5,
          memory: 2048,
          network: true
        }
      },
      {
        name: 'company_research',
        description: 'Research target companies and their hiring practices',
        inputSchema: {
          type: 'object',
          properties: {
            companyNames: { type: 'array' },
            researchDepth: { type: 'string' },
            focusAreas: { type: 'array' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            companies: { type: 'array' },
            insights: { type: 'object' },
            strategies: { type: 'array' }
          }
        },
        estimatedDuration: 600000, // 10 minutes
        resourceRequirements: {
          cpu: 2,
          memory: 3072,
          network: true
        }
      },
      {
        name: 'job_discovery',
        description: 'Discover and analyze relevant job opportunities',
        inputSchema: {
          type: 'object',
          properties: {
            searchCriteria: { type: 'object' },
            maxResults: { type: 'number' },
            platforms: { type: 'array' }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            opportunities: { type: 'array' },
            summary: { type: 'object' },
            recommendations: { type: 'array' }
          }
        },
        estimatedDuration: 900000, // 15 minutes
        resourceRequirements: {
          cpu: 2.5,
          memory: 4096,
          network: true
        }
      }
    ];

    super('ResearchAgent', capabilities, redis, logger);
    this.initializeBrowserPool();
  }

  private async initializeBrowserPool(): Promise<void> {
    for (let i = 0; i < this.maxBrowsers; i++) {
      const browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--disable-web-security'
        ]
      });
      this.browserPool.push(browser);
    }
    this.logger.info(`Initialized browser pool with ${this.maxBrowsers} browsers`);
  }

  private async getBrowser(): Promise<Browser> {
    if (this.browserPool.length > 0) {
      return this.browserPool.pop()!;
    }
    
    // Create new browser if pool is empty
    return await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    });
  }

  private async returnBrowser(browser: Browser): Promise<void> {
    if (this.browserPool.length < this.maxBrowsers) {
      this.browserPool.push(browser);
    } else {
      await browser.close();
    }
  }

  protected async handleMessage(message: AgentMessage): Promise<void> {
    switch (message.type) {
      case 'market_analysis_request':
        await this.handleMarketAnalysisRequest(message);
        break;
      case 'company_research_request':
        await this.handleCompanyResearchRequest(message);
        break;
      case 'job_discovery_request':
        await this.handleJobDiscoveryRequest(message);
        break;
      default:
        this.logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  protected async executeTask(task: AgentTask): Promise<any> {
    switch (task.type) {
      case 'market_analysis':
        return await this.analyzeJobMarket(task.data);
      case 'company_research':
        return await this.researchCompanies(task.data);
      case 'job_discovery':
        return await this.discoverJobs(task.data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  private async analyzeJobMarket(input: JobMarketAnalysisInput): Promise<any> {
    const browser = await this.getBrowser();
    
    try {
      const trends: MarketTrend[] = [];
      const insights = {
        totalJobs: 0,
        averageSalary: 0,
        topSkills: [] as string[],
        growthAreas: [] as string[],
        competitionLevel: 'medium' as 'low' | 'medium' | 'high'
      };

      // Analyze each target role
      for (const role of input.targetRoles) {
        const roleTrends = await this.analyzeRoleMarket(browser, role, input);
        trends.push(...roleTrends);
      }

      // Aggregate insights
      insights.totalJobs = trends.reduce((sum, trend) => sum + trend.jobCount, 0);
      insights.averageSalary = trends.reduce((sum, trend) => sum + trend.averageSalary, 0) / trends.length;
      insights.topSkills = this.extractTopSkills(trends);
      insights.growthAreas = trends.filter(t => t.growth > 10).map(t => t.skill);
      insights.competitionLevel = this.calculateCompetitionLevel(insights.totalJobs, input.targetRoles.length);

      // Generate recommendations
      const recommendations = this.generateMarketRecommendations(trends, insights, input);

      return {
        trends,
        insights,
        recommendations,
        analysisDate: new Date(),
        dataSource: 'multi-platform-aggregation'
      };

    } finally {
      await this.returnBrowser(browser);
    }
  }

  private async analyzeRoleMarket(browser: Browser, role: string, input: JobMarketAnalysisInput): Promise<MarketTrend[]> {
    const page = await browser.newPage();
    const trends: MarketTrend[] = [];

    try {
      // Set user agent and viewport
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      await page.setViewport({ width: 1920, height: 1080 });

      // Scrape LinkedIn for job data
      const linkedinData = await this.scrapeLinkedInJobs(page, role, input.location);
      
      // Scrape Indeed for salary data
      const indeedData = await this.scrapeIndeedSalary(page, role, input.location);
      
      // Scrape Glassdoor for company insights
      const glassdoorData = await this.scrapeGlassdoorTrends(page, role);

      // Combine and normalize data
      const trend: MarketTrend = {
        skill: role,
        demand: this.calculateDemand(linkedinData.jobCount, indeedData.jobCount),
        growth: this.calculateGrowth(linkedinData.recentPostings, linkedinData.totalPostings),
        averageSalary: (indeedData.averageSalary + glassdoorData.averageSalary) / 2,
        jobCount: linkedinData.jobCount + indeedData.jobCount,
        locations: [...new Set([...linkedinData.locations, ...indeedData.locations])]
      };

      trends.push(trend);

    } catch (error) {
      this.logger.error(`Error analyzing role market for ${role}: ${error}`);
    } finally {
      await page.close();
    }

    return trends;
  }

  private async scrapeLinkedInJobs(page: Page, role: string, locations: string[]): Promise<any> {
    try {
      const searchUrl = `https://www.linkedin.com/jobs/search/?keywords=${encodeURIComponent(role)}&location=${encodeURIComponent(locations.join(' OR '))}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      // Wait for job listings to load
      await page.waitForSelector('.jobs-search__results-list', { timeout: 10000 });

      // Extract job data
      const jobData = await page.evaluate(() => {
        const jobElements = document.querySelectorAll('.job-search-card');
        const jobs = Array.from(jobElements).map(element => {
          const titleElement = element.querySelector('.job-search-card__title');
          const companyElement = element.querySelector('.job-search-card__subtitle');
          const locationElement = element.querySelector('.job-search-card__location');
          
          return {
            title: titleElement?.textContent?.trim() || '',
            company: companyElement?.textContent?.trim() || '',
            location: locationElement?.textContent?.trim() || ''
          };
        });

        return {
          jobCount: jobs.length,
          recentPostings: Math.floor(jobs.length * 0.3), // Estimate 30% are recent
          totalPostings: jobs.length,
          locations: [...new Set(jobs.map(job => job.location).filter(Boolean))]
        };
      });

      return jobData;

    } catch (error) {
      this.logger.error(`Error scraping LinkedIn: ${error}`);
      return { jobCount: 0, recentPostings: 0, totalPostings: 0, locations: [] };
    }
  }

  private async scrapeIndeedSalary(page: Page, role: string, locations: string[]): Promise<any> {
    try {
      const searchUrl = `https://www.indeed.com/jobs?q=${encodeURIComponent(role)}&l=${encodeURIComponent(locations[0] || 'United States')}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      // Extract salary and job count data
      const salaryData = await page.evaluate(() => {
        const jobElements = document.querySelectorAll('[data-jk]');
        const salaries: number[] = [];
        const locations: string[] = [];

        jobElements.forEach(element => {
          const salaryElement = element.querySelector('.salary-snippet');
          const locationElement = element.querySelector('.companyLocation');
          
          if (salaryElement) {
            const salaryText = salaryElement.textContent || '';
            const salaryMatch = salaryText.match(/\$(\d+,?\d*)/);
            if (salaryMatch) {
              const salary = parseInt(salaryMatch[1].replace(',', ''));
              if (salary > 20000 && salary < 500000) { // Reasonable salary range
                salaries.push(salary);
              }
            }
          }

          if (locationElement) {
            locations.push(locationElement.textContent?.trim() || '');
          }
        });

        return {
          jobCount: jobElements.length,
          averageSalary: salaries.length > 0 ? salaries.reduce((a, b) => a + b, 0) / salaries.length : 0,
          locations: [...new Set(locations.filter(Boolean))]
        };
      });

      return salaryData;

    } catch (error) {
      this.logger.error(`Error scraping Indeed: ${error}`);
      return { jobCount: 0, averageSalary: 0, locations: [] };
    }
  }

  private async scrapeGlassdoorTrends(page: Page, role: string): Promise<any> {
    try {
      const searchUrl = `https://www.glassdoor.com/Job/jobs.htm?sc.keyword=${encodeURIComponent(role)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      // Extract trend data
      const trendData = await page.evaluate(() => {
        const salaryElements = document.querySelectorAll('[data-test="salary-estimate"]');
        const salaries: number[] = [];

        salaryElements.forEach(element => {
          const salaryText = element.textContent || '';
          const salaryMatch = salaryText.match(/\$(\d+)K/);
          if (salaryMatch) {
            salaries.push(parseInt(salaryMatch[1]) * 1000);
          }
        });

        return {
          averageSalary: salaries.length > 0 ? salaries.reduce((a, b) => a + b, 0) / salaries.length : 0,
          dataPoints: salaries.length
        };
      });

      return trendData;

    } catch (error) {
      this.logger.error(`Error scraping Glassdoor: ${error}`);
      return { averageSalary: 0, dataPoints: 0 };
    }
  }

  private calculateDemand(linkedinJobs: number, indeedJobs: number): 'high' | 'medium' | 'low' {
    const totalJobs = linkedinJobs + indeedJobs;
    if (totalJobs > 1000) return 'high';
    if (totalJobs > 300) return 'medium';
    return 'low';
  }

  private calculateGrowth(recentPostings: number, totalPostings: number): number {
    if (totalPostings === 0) return 0;
    return Math.round((recentPostings / totalPostings) * 100);
  }

  private extractTopSkills(trends: MarketTrend[]): string[] {
    return trends
      .filter(trend => trend.demand === 'high')
      .sort((a, b) => b.jobCount - a.jobCount)
      .slice(0, 10)
      .map(trend => trend.skill);
  }

  private calculateCompetitionLevel(totalJobs: number, roleCount: number): 'low' | 'medium' | 'high' {
    const avgJobsPerRole = totalJobs / roleCount;
    if (avgJobsPerRole > 500) return 'low';
    if (avgJobsPerRole > 200) return 'medium';
    return 'high';
  }

  private generateMarketRecommendations(trends: MarketTrend[], insights: any, input: JobMarketAnalysisInput): string[] {
    const recommendations: string[] = [];

    // Skill recommendations
    if (insights.topSkills.length > 0) {
      recommendations.push(`Focus on developing skills in: ${insights.topSkills.slice(0, 3).join(', ')}`);
    }

    // Salary recommendations
    if (insights.averageSalary > 0) {
      recommendations.push(`Target salary range: $${Math.round(insights.averageSalary * 0.9).toLocaleString()} - $${Math.round(insights.averageSalary * 1.1).toLocaleString()}`);
    }

    // Location recommendations
    const topLocations = trends.flatMap(t => t.locations).slice(0, 3);
    if (topLocations.length > 0) {
      recommendations.push(`Consider opportunities in: ${topLocations.join(', ')}`);
    }

    // Growth area recommendations
    if (insights.growthAreas.length > 0) {
      recommendations.push(`High-growth areas to explore: ${insights.growthAreas.slice(0, 3).join(', ')}`);
    }

    // Competition recommendations
    if (insights.competitionLevel === 'high') {
      recommendations.push('Market is competitive - focus on unique skills and strong portfolio');
    } else if (insights.competitionLevel === 'low') {
      recommendations.push('Market has good opportunities - consider applying to multiple positions');
    }

    return recommendations;
  }

  private async researchCompanies(input: CompanyResearchInput): Promise<any> {
    const browser = await this.getBrowser();
    const companies: CompanyInsight[] = [];

    try {
      for (const companyName of input.companyNames) {
        const insight = await this.researchSingleCompany(browser, companyName, input.researchDepth);
        companies.push(insight);
      }

      return {
        companies,
        researchDate: new Date(),
        strategies: this.generateCompanyStrategies(companies),
        insights: this.aggregateCompanyInsights(companies)
      };

    } finally {
      await this.returnBrowser(browser);
    }
  }

  private async researchSingleCompany(browser: Browser, companyName: string, depth: string): Promise<CompanyInsight> {
    const page = await browser.newPage();
    
    try {
      // Research company on multiple platforms
      const glassdoorData = await this.scrapeGlassdoorCompany(page, companyName);
      const linkedinData = await this.scrapeLinkedInCompany(page, companyName);
      const newsData = await this.scrapeCompanyNews(page, companyName);

      return {
        name: companyName,
        industry: glassdoorData.industry || 'Unknown',
        size: glassdoorData.size || 'Unknown',
        culture: {
          workLifeBalance: glassdoorData.workLifeBalance || 3.0,
          diversity: glassdoorData.diversity || 3.0,
          innovation: glassdoorData.innovation || 3.0,
          compensation: glassdoorData.compensation || 3.0
        },
        hiringTrends: {
          activeRoles: linkedinData.activeRoles || 0,
          averageTimeToHire: 30, // Default estimate
          preferredSkills: linkedinData.preferredSkills || []
        },
        applicationTips: this.generateApplicationTips(glassdoorData, linkedinData),
        recentNews: newsData.headlines || []
      };

    } finally {
      await page.close();
    }
  }

  private async scrapeGlassdoorCompany(page: Page, companyName: string): Promise<any> {
    try {
      const searchUrl = `https://www.glassdoor.com/Overview/Working-at-${encodeURIComponent(companyName.replace(/\s+/g, '-'))}-EI_IE.htm`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      return await page.evaluate(() => {
        const ratingElements = document.querySelectorAll('.rating');
        const industryElement = document.querySelector('.industry');
        const sizeElement = document.querySelector('.size');

        return {
          industry: industryElement?.textContent?.trim() || '',
          size: sizeElement?.textContent?.trim() || '',
          workLifeBalance: 3.5, // Mock data - would extract from actual ratings
          diversity: 3.2,
          innovation: 3.8,
          compensation: 3.6
        };
      });

    } catch (error) {
      this.logger.error(`Error scraping Glassdoor for ${companyName}: ${error}`);
      return {};
    }
  }

  private async scrapeLinkedInCompany(page: Page, companyName: string): Promise<any> {
    try {
      const searchUrl = `https://www.linkedin.com/company/${encodeURIComponent(companyName.toLowerCase().replace(/\s+/g, '-'))}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      return await page.evaluate(() => {
        const jobElements = document.querySelectorAll('.job-card');
        const skillElements = document.querySelectorAll('.skill-pill');

        return {
          activeRoles: jobElements.length,
          preferredSkills: Array.from(skillElements).map(el => el.textContent?.trim()).filter(Boolean).slice(0, 10)
        };
      });

    } catch (error) {
      this.logger.error(`Error scraping LinkedIn for ${companyName}: ${error}`);
      return { activeRoles: 0, preferredSkills: [] };
    }
  }

  private async scrapeCompanyNews(page: Page, companyName: string): Promise<any> {
    try {
      const searchUrl = `https://news.google.com/search?q=${encodeURIComponent(companyName)}&hl=en-US&gl=US&ceid=US:en`;
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      return await page.evaluate(() => {
        const headlineElements = document.querySelectorAll('article h3');
        return {
          headlines: Array.from(headlineElements)
            .map(el => el.textContent?.trim())
            .filter(Boolean)
            .slice(0, 5)
        };
      });

    } catch (error) {
      this.logger.error(`Error scraping news for ${companyName}: ${error}`);
      return { headlines: [] };
    }
  }

  private generateApplicationTips(glassdoorData: any, linkedinData: any): string[] {
    const tips: string[] = [];

    if (linkedinData.preferredSkills?.length > 0) {
      tips.push(`Highlight experience with: ${linkedinData.preferredSkills.slice(0, 3).join(', ')}`);
    }

    if (glassdoorData.workLifeBalance > 4.0) {
      tips.push('Company values work-life balance - mention your time management skills');
    }

    if (glassdoorData.innovation > 4.0) {
      tips.push('Emphasize innovative projects and creative problem-solving experience');
    }

    tips.push('Research recent company news and mention relevant developments in your cover letter');
    tips.push('Connect with current employees on LinkedIn before applying');

    return tips;
  }

  private generateCompanyStrategies(companies: CompanyInsight[]): string[] {
    const strategies: string[] = [];

    const avgCulture = companies.reduce((sum, c) => sum + Object.values(c.culture).reduce((a, b) => a + b, 0), 0) / companies.length;
    
    if (avgCulture > 14) {
      strategies.push('Target companies with strong culture ratings - tailor applications to emphasize cultural fit');
    }

    const totalActiveRoles = companies.reduce((sum, c) => sum + c.hiringTrends.activeRoles, 0);
    if (totalActiveRoles > 50) {
      strategies.push('Multiple companies are actively hiring - consider applying to several positions simultaneously');
    }

    strategies.push('Customize resume and cover letter for each company based on their specific culture and requirements');
    strategies.push('Leverage company news and recent developments in your application materials');

    return strategies;
  }

  private aggregateCompanyInsights(companies: CompanyInsight[]): any {
    return {
      totalCompanies: companies.length,
      averageCultureScore: companies.reduce((sum, c) => sum + Object.values(c.culture).reduce((a, b) => a + b, 0), 0) / companies.length,
      totalActiveRoles: companies.reduce((sum, c) => sum + c.hiringTrends.activeRoles, 0),
      topIndustries: [...new Set(companies.map(c => c.industry))].slice(0, 5),
      commonSkills: this.findCommonSkills(companies)
    };
  }

  private findCommonSkills(companies: CompanyInsight[]): string[] {
    const skillCounts = new Map<string, number>();
    
    companies.forEach(company => {
      company.hiringTrends.preferredSkills.forEach(skill => {
        skillCounts.set(skill, (skillCounts.get(skill) || 0) + 1);
      });
    });

    return Array.from(skillCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([skill]) => skill);
  }

  private async discoverJobs(input: JobDiscoveryInput): Promise<any> {
    const browser = await this.getBrowser();
    const opportunities: JobOpportunity[] = [];

    try {
      for (const platform of input.platforms) {
        const platformJobs = await this.scrapeJobsFromPlatform(browser, platform, input);
        opportunities.push(...platformJobs);
      }

      // Sort by match score and limit results
      const sortedOpportunities = opportunities
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, input.maxResults);

      return {
        opportunities: sortedOpportunities,
        summary: {
          totalFound: opportunities.length,
          platforms: input.platforms,
          averageMatchScore: opportunities.reduce((sum, job) => sum + job.matchScore, 0) / opportunities.length,
          topCompanies: this.getTopCompanies(opportunities),
          salaryRange: this.calculateSalaryRange(opportunities)
        },
        recommendations: this.generateJobRecommendations(sortedOpportunities)
      };

    } finally {
      await this.returnBrowser(browser);
    }
  }

  private async scrapeJobsFromPlatform(browser: Browser, platform: string, input: JobDiscoveryInput): Promise<JobOpportunity[]> {
    const page = await browser.newPage();
    const jobs: JobOpportunity[] = [];

    try {
      const baseUrl = this.jobBoardUrls.get(platform);
      if (!baseUrl) {
        this.logger.warn(`Unknown platform: ${platform}`);
        return jobs;
      }

      // Build search URL based on platform
      const searchUrl = this.buildSearchUrl(baseUrl, input.searchCriteria);
      await page.goto(searchUrl, { waitUntil: 'networkidle0' });

      // Platform-specific scraping logic
      const platformJobs = await this.extractJobsFromPage(page, platform, input.searchCriteria);
      jobs.push(...platformJobs);

    } catch (error) {
      this.logger.error(`Error scraping ${platform}: ${error}`);
    } finally {
      await page.close();
    }

    return jobs;
  }

  private buildSearchUrl(baseUrl: string, criteria: any): string {
    const params = new URLSearchParams();
    
    if (criteria.roles?.length > 0) {
      params.append('q', criteria.roles.join(' OR '));
    }
    
    if (criteria.location?.length > 0) {
      params.append('l', criteria.location[0]);
    }
    
    if (criteria.remote) {
      params.append('remote', 'true');
    }

    return `${baseUrl}?${params.toString()}`;
  }

  private async extractJobsFromPage(page: Page, platform: string, criteria: any): Promise<JobOpportunity[]> {
    // This would contain platform-specific extraction logic
    // For now, returning mock data structure
    return await page.evaluate((platform, criteria) => {
      // Mock job extraction - would be replaced with actual scraping logic
      return [];
    }, platform, criteria);
  }

  private getTopCompanies(opportunities: JobOpportunity[]): string[] {
    const companyCounts = new Map<string, number>();
    
    opportunities.forEach(job => {
      companyCounts.set(job.company, (companyCounts.get(job.company) || 0) + 1);
    });

    return Array.from(companyCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([company]) => company);
  }

  private calculateSalaryRange(opportunities: JobOpportunity[]): { min: number; max: number } {
    const salaries = opportunities
      .filter(job => job.salary)
      .flatMap(job => [job.salary!.min, job.salary!.max]);

    if (salaries.length === 0) {
      return { min: 0, max: 0 };
    }

    return {
      min: Math.min(...salaries),
      max: Math.max(...salaries)
    };
  }

  private generateJobRecommendations(opportunities: JobOpportunity[]): string[] {
    const recommendations: string[] = [];

    if (opportunities.length > 0) {
      const avgMatchScore = opportunities.reduce((sum, job) => sum + job.matchScore, 0) / opportunities.length;
      
      if (avgMatchScore > 80) {
        recommendations.push('Excellent matches found - prioritize applications to top-scoring positions');
      } else if (avgMatchScore > 60) {
        recommendations.push('Good matches available - consider tailoring resume for better alignment');
      } else {
        recommendations.push('Limited matches - consider expanding search criteria or developing additional skills');
      }
    }

    const remoteJobs = opportunities.filter(job => job.remote).length;
    if (remoteJobs > opportunities.length * 0.5) {
      recommendations.push('Many remote opportunities available - highlight remote work experience');
    }

    recommendations.push('Apply within 48 hours of job posting for best response rates');
    recommendations.push('Research company culture and recent news before applying');

    return recommendations;
  }

  private async handleMarketAnalysisRequest(message: AgentMessage): Promise<void> {
    this.logger.info('Market analysis request received', message.payload);
  }

  private async handleCompanyResearchRequest(message: AgentMessage): Promise<void> {
    this.logger.info('Company research request received', message.payload);
  }

  private async handleJobDiscoveryRequest(message: AgentMessage): Promise<void> {
    this.logger.info('Job discovery request received', message.payload);
  }

  public async shutdown(): Promise<void> {
    // Close all browsers in the pool
    await Promise.all(this.browserPool.map(browser => browser.close()));
    this.browserPool = [];
    this.logger.info('Research agent shutdown complete');
  }
}
