import { EventEmitter } from 'events';
import { Redis } from 'ioredis';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';

export interface AgentMessage {
  id: string;
  type: string;
  payload: any;
  sender: string;
  recipient: string;
  timestamp: Date;
  correlationId?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface AgentTask {
  id: string;
  type: string;
  userId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  data: any;
  dependencies: string[];
  assignedAgent?: string;
  createdAt: Date;
  updatedAt: Date;
  deadline?: Date;
}

export interface AgentCapability {
  name: string;
  description: string;
  inputSchema: any;
  outputSchema: any;
  estimatedDuration: number;
  resourceRequirements: {
    cpu: number;
    memory: number;
    network: boolean;
  };
}

export abstract class BaseAgent extends EventEmitter {
  protected id: string;
  protected name: string;
  protected capabilities: AgentCapability[];
  protected status: 'idle' | 'busy' | 'error' | 'offline';
  protected currentTask?: AgentTask;
  protected redis: Redis;
  protected logger: Logger;

  constructor(
    name: string,
    capabilities: AgentCapability[],
    redis: Redis,
    logger: Logger
  ) {
    super();
    this.id = uuidv4();
    this.name = name;
    this.capabilities = capabilities;
    this.status = 'idle';
    this.redis = redis;
    this.logger = logger;
    
    this.setupMessageHandling();
  }

  private setupMessageHandling() {
    // Subscribe to agent-specific channel
    this.redis.subscribe(`agent:${this.id}`, (err) => {
      if (err) {
        this.logger.error(`Failed to subscribe to agent channel: ${err.message}`);
      }
    });

    this.redis.on('message', (channel, message) => {
      if (channel === `agent:${this.id}`) {
        try {
          const agentMessage: AgentMessage = JSON.parse(message);
          this.handleMessage(agentMessage);
        } catch (error) {
          this.logger.error(`Failed to parse message: ${error}`);
        }
      }
    });
  }

  protected async sendMessage(message: AgentMessage): Promise<void> {
    const channel = message.recipient === 'broadcast' 
      ? 'agents:broadcast' 
      : `agent:${message.recipient}`;
    
    await this.redis.publish(channel, JSON.stringify(message));
    
    this.logger.debug(`Message sent from ${this.id} to ${message.recipient}`, {
      messageId: message.id,
      type: message.type
    });
  }

  protected abstract handleMessage(message: AgentMessage): Promise<void>;
  protected abstract executeTask(task: AgentTask): Promise<any>;

  public async assignTask(task: AgentTask): Promise<void> {
    if (this.status !== 'idle') {
      throw new Error(`Agent ${this.id} is not available (status: ${this.status})`);
    }

    this.currentTask = task;
    this.status = 'busy';
    
    try {
      this.logger.info(`Agent ${this.id} starting task ${task.id}`);
      const result = await this.executeTask(task);
      
      await this.completeTask(task.id, result);
    } catch (error) {
      await this.failTask(task.id, error);
    }
  }

  private async completeTask(taskId: string, result: any): Promise<void> {
    this.status = 'idle';
    this.currentTask = undefined;
    
    await this.sendMessage({
      id: uuidv4(),
      type: 'task_completed',
      payload: { taskId, result },
      sender: this.id,
      recipient: 'orchestrator',
      timestamp: new Date(),
      priority: 'medium'
    });
    
    this.logger.info(`Task ${taskId} completed successfully`);
  }

  private async failTask(taskId: string, error: any): Promise<void> {
    this.status = 'error';
    this.currentTask = undefined;
    
    await this.sendMessage({
      id: uuidv4(),
      type: 'task_failed',
      payload: { taskId, error: error.message },
      sender: this.id,
      recipient: 'orchestrator',
      timestamp: new Date(),
      priority: 'high'
    });
    
    this.logger.error(`Task ${taskId} failed: ${error.message}`);
  }

  public getCapabilities(): AgentCapability[] {
    return this.capabilities;
  }

  public getStatus(): string {
    return this.status;
  }

  public getId(): string {
    return this.id;
  }
}

export class AgentOrchestrator extends EventEmitter {
  private agents: Map<string, BaseAgent> = new Map();
  private taskQueue: AgentTask[] = [];
  private runningTasks: Map<string, AgentTask> = new Map();
  private redis: Redis;
  private logger: Logger;
  private isRunning: boolean = false;

  constructor(redis: Redis, logger: Logger) {
    super();
    this.redis = redis;
    this.logger = logger;
    this.setupMessageHandling();
  }

  private setupMessageHandling() {
    this.redis.subscribe('orchestrator', (err) => {
      if (err) {
        this.logger.error(`Failed to subscribe to orchestrator channel: ${err.message}`);
      }
    });

    this.redis.on('message', (channel, message) => {
      if (channel === 'orchestrator') {
        try {
          const agentMessage: AgentMessage = JSON.parse(message);
          this.handleAgentMessage(agentMessage);
        } catch (error) {
          this.logger.error(`Failed to parse orchestrator message: ${error}`);
        }
      }
    });
  }

  private async handleAgentMessage(message: AgentMessage): Promise<void> {
    switch (message.type) {
      case 'task_completed':
        await this.handleTaskCompleted(message.payload);
        break;
      case 'task_failed':
        await this.handleTaskFailed(message.payload);
        break;
      case 'agent_status_update':
        await this.handleAgentStatusUpdate(message.payload);
        break;
      default:
        this.logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  public registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.getId(), agent);
    this.logger.info(`Agent registered: ${agent.getId()}`);
  }

  public async submitTask(task: AgentTask): Promise<string> {
    task.id = task.id || uuidv4();
    task.status = 'pending';
    task.createdAt = new Date();
    task.updatedAt = new Date();
    
    this.taskQueue.push(task);
    this.taskQueue.sort((a, b) => b.priority - a.priority);
    
    this.logger.info(`Task submitted: ${task.id}`, { type: task.type, userId: task.userId });
    
    if (this.isRunning) {
      setImmediate(() => this.processTaskQueue());
    }
    
    return task.id;
  }

  private async processTaskQueue(): Promise<void> {
    while (this.taskQueue.length > 0) {
      const task = this.taskQueue.shift()!;
      
      // Check dependencies
      if (!this.areDependenciesMet(task)) {
        this.taskQueue.push(task); // Re-queue for later
        continue;
      }
      
      const suitableAgent = this.findSuitableAgent(task);
      if (!suitableAgent) {
        this.taskQueue.unshift(task); // Put back at front
        break; // No available agents
      }
      
      try {
        task.status = 'running';
        task.assignedAgent = suitableAgent.getId();
        task.updatedAt = new Date();
        
        this.runningTasks.set(task.id, task);
        await suitableAgent.assignTask(task);
        
      } catch (error) {
        this.logger.error(`Failed to assign task ${task.id}: ${error}`);
        task.status = 'failed';
        this.runningTasks.delete(task.id);
      }
    }
  }

  private areDependenciesMet(task: AgentTask): boolean {
    return task.dependencies.every(depId => {
      const depTask = this.runningTasks.get(depId);
      return !depTask || depTask.status === 'completed';
    });
  }

  private findSuitableAgent(task: AgentTask): BaseAgent | null {
    for (const [id, agent] of this.agents) {
      if (agent.getStatus() === 'idle') {
        const capabilities = agent.getCapabilities();
        const hasCapability = capabilities.some(cap => cap.name === task.type);
        if (hasCapability) {
          return agent;
        }
      }
    }
    return null;
  }

  private async handleTaskCompleted(payload: any): Promise<void> {
    const { taskId, result } = payload;
    const task = this.runningTasks.get(taskId);
    
    if (task) {
      task.status = 'completed';
      task.updatedAt = new Date();
      this.runningTasks.delete(taskId);
      
      this.emit('taskCompleted', { task, result });
      this.logger.info(`Task completed: ${taskId}`);
      
      // Process queue in case this completion unblocks other tasks
      setImmediate(() => this.processTaskQueue());
    }
  }

  private async handleTaskFailed(payload: any): Promise<void> {
    const { taskId, error } = payload;
    const task = this.runningTasks.get(taskId);
    
    if (task) {
      task.status = 'failed';
      task.updatedAt = new Date();
      this.runningTasks.delete(taskId);
      
      this.emit('taskFailed', { task, error });
      this.logger.error(`Task failed: ${taskId} - ${error}`);
    }
  }

  private async handleAgentStatusUpdate(payload: any): Promise<void> {
    // Handle agent status updates
    this.logger.debug('Agent status update received', payload);
  }

  public start(): void {
    this.isRunning = true;
    this.logger.info('Agent orchestrator started');
    this.processTaskQueue();
  }

  public stop(): void {
    this.isRunning = false;
    this.logger.info('Agent orchestrator stopped');
  }

  public getStats(): any {
    return {
      totalAgents: this.agents.size,
      queuedTasks: this.taskQueue.length,
      runningTasks: this.runningTasks.size,
      agentStatuses: Array.from(this.agents.values()).map(agent => ({
        id: agent.getId(),
        status: agent.getStatus(),
        capabilities: agent.getCapabilities().map(cap => cap.name)
      }))
    };
  }
}
